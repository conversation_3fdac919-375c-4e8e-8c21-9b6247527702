#!/bin/bash

# CS2 Launch Script with Cheat Injection

CHEAT_LIB="/home/<USER>/Desktop/cs2-internal/build/bin/libcs2-cheat.so"
STEAM_PATH="$HOME/.local/share/Steam"
CS2_PATH="$STEAM_PATH/steamapps/common/Counter-Strike Global Offensive"

echo "CS2 Cheat Launcher"
echo "=================="

# Check if cheat library exists
if [ ! -f "$CHEAT_LIB" ]; then
    echo "Error: Cheat library not found at $CHEAT_LIB"
    echo "Please build the project first with: ./build.sh"
    exit 1
fi

# Check if CS2 is installed
if [ ! -d "$CS2_PATH" ]; then
    echo "Error: CS2 not found at $CS2_PATH"
    echo "Please check your Steam installation path"
    exit 1
fi

echo "Cheat library: $CHEAT_LIB"
echo "CS2 path: $CS2_PATH"
echo ""

# Set environment variables
export LD_PRELOAD="$CHEAT_LIB"
export STEAM_COMPAT_DATA_PATH="$STEAM_PATH/steamapps/compatdata"

echo "Launching CS2 with cheat injection..."
echo "Launch arguments: -insecure -allow_third_party_software"
echo ""

# Launch CS2 through Steam
steam steam://rungameid/730 -insecure -allow_third_party_software

echo "CS2 launched. Check the game for the cheat menu!"
echo "If the menu doesn't appear, check the terminal output for error messages."
