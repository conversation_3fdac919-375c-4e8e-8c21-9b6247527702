cmake_minimum_required(VERSION 3.16)
project(cs2-internal)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(OpenGL REQUIRED)
find_package(X11 REQUIRED)

pkg_check_modules(GTK3 REQUIRED gtk+-3.0)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/imgui)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/imgui/backends)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

# ImGui source files
set(IMGUI_SOURCES
    external/imgui/imgui.cpp
    external/imgui/imgui_demo.cpp
    external/imgui/imgui_draw.cpp
    external/imgui/imgui_tables.cpp
    external/imgui/imgui_widgets.cpp
    external/imgui/backends/imgui_impl_opengl3.cpp
    external/imgui/backends/imgui_impl_glfw.cpp
)

# Our source files
set(CHEAT_SOURCES
    src/main.cpp
    src/hooks.cpp
    src/gui.cpp
)

# Create shared library (for injection)
add_library(cs2-cheat SHARED ${CHEAT_SOURCES} ${IMGUI_SOURCES})

# Link libraries
target_link_libraries(cs2-cheat 
    ${OPENGL_LIBRARIES}
    ${X11_LIBRARIES}
    ${GTK3_LIBRARIES}
    dl
    pthread
)

# Compiler flags
target_compile_options(cs2-cheat PRIVATE 
    -fPIC 
    -Wall 
    -Wextra
    ${GTK3_CFLAGS_OTHER}
)

# Set output directory
set_target_properties(cs2-cheat PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
