#!/bin/bash

# Debug injection script with detailed error reporting

CS2_PID=$(pgrep -x cs2)
CHEAT_LIB="$(realpath ./build/bin/libcs2-cheat.so)"

if [ -z "$CS2_PID" ]; then
    echo "CS2 is not running!"
    exit 1
fi

echo "Debug Injection Test"
echo "==================="
echo "CS2 PID: $CS2_PID"
echo "Library: $CHEAT_LIB"
echo ""

# Check library dependencies
echo "=== Library Dependencies ==="
echo "Checking if our library can be loaded..."
ldd "$CHEAT_LIB"
echo ""

# Test library loading in current process
echo "=== Testing Library Loading ==="
echo "Testing dlopen in current process..."
cat > /tmp/test_dlopen.c << EOF
#include <dlfcn.h>
#include <stdio.h>

int main() {
    printf("Testing dlopen with: $CHEAT_LIB\n");
    
    void* handle = dlopen("$CHEAT_LIB", RTLD_LAZY);
    if (handle) {
        printf("SUCCESS: Library loaded at %p\n", handle);
        dlclose(handle);
        return 0;
    } else {
        printf("FAILED: %s\n", dlerror());
        return 1;
    }
}
EOF

gcc -o /tmp/test_dlopen /tmp/test_dlopen.c -ldl
/tmp/test_dlopen
echo ""

# Detailed GDB injection with error checking
echo "=== Detailed GDB Injection ==="
cat > /tmp/debug_inject.gdb << EOF
set confirm off
set pagination off
attach $CS2_PID

printf "=== Before injection ===\n"
info proc mappings

printf "\n=== Attempting dlopen ===\n"
set \$handle = (void*)dlopen("$CHEAT_LIB", 2)
printf "dlopen returned: %p\n", \$handle

if (\$handle == 0)
    printf "dlopen FAILED\n"
    set \$error = (char*)dlerror()
    if (\$error != 0)
        printf "Error: %s\n", \$error
    else
        printf "No error message available\n"
    end
else
    printf "dlopen SUCCESS!\n"
    printf "Library loaded at: %p\n", \$handle
end

printf "\n=== After injection ===\n"
info proc mappings

detach
quit
EOF

echo "Running detailed GDB injection..."
gdb -batch -x /tmp/debug_inject.gdb 2>/dev/null

# Cleanup
rm -f /tmp/test_dlopen /tmp/test_dlopen.c /tmp/debug_inject.gdb

echo ""
echo "=== Final Check ==="
if grep -q "libcs2-cheat.so" /proc/$CS2_PID/maps; then
    echo "✓ SUCCESS: Library is now loaded!"
    grep "libcs2-cheat.so" /proc/$CS2_PID/maps
else
    echo "✗ FAILED: Library still not loaded"
    echo ""
    echo "Possible issues:"
    echo "1. Missing dependencies (check ldd output above)"
    echo "2. CS2 process protection/sandboxing"
    echo "3. Library initialization failure"
    echo "4. Incompatible library architecture"
    echo ""
    echo "Try the LD_PRELOAD method instead:"
    echo "LD_PRELOAD=\"$CHEAT_LIB\" steam steam://rungameid/730 -insecure -allow_third_party_software"
fi
