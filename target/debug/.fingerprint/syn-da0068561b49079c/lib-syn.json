{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 7227139478284024997, "deps": [[1988483478007900009, "unicode_ident", false, 14649654812033060143], [3060637413840920116, "proc_macro2", false, 10203851903637055244], [17990358020177143287, "quote", false, 10905874835787458737]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-da0068561b49079c/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}