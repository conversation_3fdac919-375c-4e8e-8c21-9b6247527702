{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"fs\", \"libc-extra-traits\", \"param\", \"process\", \"std\", \"system\", \"thread\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 15904350142235955753, "path": 15624965406587934003, "deps": [[3430646239657634944, "build_script_build", false, 8457018141791859873], [5036304442846774733, "linux_raw_sys", false, 7343861339862125115], [7896293946984509699, "bitflags", false, 9015183589251172971]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-1b498f3ece37a6c8/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}