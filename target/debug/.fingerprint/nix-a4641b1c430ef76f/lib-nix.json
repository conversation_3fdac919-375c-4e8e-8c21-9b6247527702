{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 2241668132362809309, "path": 15965424848077839333, "deps": [[2828590642173593838, "cfg_if", false, 18388090790109599755], [4684437522915235464, "libc", false, 4385136150961422644], [7896293946984509699, "bitflags", false, 8616709964903486567]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-a4641b1c430ef76f/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}