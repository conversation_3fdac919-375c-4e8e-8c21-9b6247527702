{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"fs\", \"libc-extra-traits\", \"param\", \"process\", \"std\", \"system\", \"thread\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 10474043801839359757, "path": 15624965406587934003, "deps": [[3430646239657634944, "build_script_build", false, 7350536075031505753], [5036304442846774733, "linux_raw_sys", false, 5890838234796390752], [7896293946984509699, "bitflags", false, 1194660789753140726]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-2b67f0cbe0d6854c/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}