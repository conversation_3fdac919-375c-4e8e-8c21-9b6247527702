{"rustc": 11410426090777951712, "features": "[\"elf\", \"errno\", \"general\", \"ioctl\", \"no_std\", \"prctl\", \"system\"]", "declared_features": "[\"bootparam\", \"btrfs\", \"compiler_builtins\", \"core\", \"default\", \"elf\", \"elf_uapi\", \"errno\", \"general\", \"if_arp\", \"if_ether\", \"if_packet\", \"io_uring\", \"ioctl\", \"landlock\", \"loop_device\", \"mempolicy\", \"net\", \"netlink\", \"no_std\", \"prctl\", \"ptrace\", \"rustc-dep-of-std\", \"std\", \"system\", \"xdp\"]", "target": 5772965225213482929, "profile": 4314370921045452772, "path": 3111751525669626510, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/linux-raw-sys-b82c7bb08b53d44d/dep-lib-linux_raw_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}