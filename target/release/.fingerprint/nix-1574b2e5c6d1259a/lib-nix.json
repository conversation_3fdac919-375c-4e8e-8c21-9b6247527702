{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 2040997289075261528, "path": 15965424848077839333, "deps": [[2828590642173593838, "cfg_if", false, 13683048012452731577], [4684437522915235464, "libc", false, 12378580632927812944], [7896293946984509699, "bitflags", false, 4806084282939060167]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/nix-1574b2e5c6d1259a/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}