{"rustc": 11410426090777951712, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 11094300728354777950, "deps": [[7312356825837975969, "crc32fast", false, 3449142309465778010], [7636735136738807108, "miniz_oxide", false, 1629474578697297248]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-44866a2d04caaa6b/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}