{"rustc": 11410426090777951712, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 13464227597933806647, "deps": [[5820056977320921005, "anstream", false, 7033323088013789043], [9394696648929125047, "anstyle", false, 3258199620985845739], [11166530783118767604, "strsim", false, 5174237281516232746], [11649982696571033535, "clap_lex", false, 14327045319344893021]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-24ac0d899e9a2962/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}