{"rustc": 11410426090777951712, "features": "[\"alloc\", \"archive\", \"default\", \"elf32\", \"elf64\", \"endian_fd\", \"log\", \"mach32\", \"mach64\", \"pe32\", \"pe64\", \"std\"]", "declared_features": "[\"alloc\", \"archive\", \"default\", \"elf32\", \"elf64\", \"endian_fd\", \"log\", \"mach32\", \"mach64\", \"pe32\", \"pe64\", \"std\"]", "target": 18332175263840725859, "profile": 2040997289075261528, "path": 16978564724343861587, "deps": [[5441587664951651985, "scroll", false, 13883600233281599108], [5986029879202738730, "log", false, 17651996395619581826], [17082805128634993388, "plain", false, 2555386867207203515]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/goblin-f2886635b2070662/dep-lib-goblin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}