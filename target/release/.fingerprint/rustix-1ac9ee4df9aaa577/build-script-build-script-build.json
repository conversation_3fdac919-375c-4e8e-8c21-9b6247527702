{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"fs\", \"libc-extra-traits\", \"param\", \"process\", \"std\", \"system\", \"thread\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 5408242616063297496, "profile": 8123407633567502970, "path": 12305650516705865483, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-1ac9ee4df9aaa577/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}