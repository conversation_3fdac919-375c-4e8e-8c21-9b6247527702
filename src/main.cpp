#include "cheat.h"

// Global variables
bool g_initialized = false;
bool g_show_menu = true;

// Constructor function - called when library is loaded
__attribute__((constructor))
void on_library_load() {
    LOG("CS2 Internal Cheat loaded!");
    
    // Initialize hooks
    Hooks::Initialize();
    
    // Initialize cheat systems
    Cheat::Initialize();
    
    LOG("Initialization complete. Press INSERT to toggle menu.");
}

// Destructor function - called when library is unloaded
__attribute__((destructor))
void on_library_unload() {
    LOG("CS2 Internal Cheat unloading...");
    
    // Cleanup
    GUI::Shutdown();
    Hooks::Shutdown();
    Cheat::Shutdown();
    
    LOG("Cleanup complete.");
}

namespace Cheat {
    void Initialize() {
        LOG("Cheat systems initialized");
        // Initialize cheat-specific functionality here
        // Memory scanning, pattern finding, etc.
    }
    
    void Shutdown() {
        LOG("Cheat systems shutdown");
        // Cleanup cheat-specific functionality
    }
    
    void Update() {
        // Main cheat logic update loop
        // This gets called every frame from the render hook
        
        // Handle input for menu toggle
        static bool insert_pressed = false;
        // Note: Proper input handling would require hooking input functions
        // For now, menu visibility is controlled by g_show_menu
    }
}
