#pragma once

#include <iostream>
#include <dlfcn.h>
#include <GL/gl.h>
#include <GL/glx.h>
#include <X11/Xlib.h>

// ImGui includes
#include "imgui.h"
#include "imgui_impl_opengl3.h"
#include "imgui_impl_glfw.h"

// Global state
extern bool g_initialized;
extern bool g_show_menu;

// Function declarations
namespace Hooks {
    void Initialize();
    void Shutdown();
    
    // OpenGL hooks
    void glXSwapBuffers(Display* dpy, GLXDrawable drawable);
}

namespace GUI {
    void Initialize();
    void Shutdown();
    void Render();
    void HandleInput();
}

namespace Cheat {
    void Initialize();
    void Shutdown();
    void Update();
}

// Utility macros
#define LOG(msg) std::cout << "[CS2-Cheat] " << msg << std::endl
#define LOG_ERROR(msg) std::cerr << "[CS2-Cheat ERROR] " << msg << std::endl

// Original function pointers
typedef void (*glXSwapBuffers_t)(Display*, GLXDrawable);
extern glXSwapBuffers_t original_glXSwapBuffers;
