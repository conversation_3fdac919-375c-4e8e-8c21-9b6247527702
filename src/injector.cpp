#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <cstring>
#include <unistd.h>
#include <sys/ptrace.h>
#include <sys/wait.h>
#include <sys/user.h>
#include <sys/mman.h>
#include <dlfcn.h>
#include <link.h>

class ProcessInjector {
private:
    pid_t target_pid;
    
    struct MemoryRegion {
        uintptr_t start;
        uintptr_t end;
        std::string permissions;
        std::string path;
    };
    
    std::vector<MemoryRegion> getMemoryMaps() {
        std::vector<MemoryRegion> regions;
        std::string maps_path = "/proc/" + std::to_string(target_pid) + "/maps";
        std::ifstream maps_file(maps_path);
        std::string line;
        
        while (std::getline(maps_file, line)) {
            MemoryRegion region;
            char perms[16];
            char path[512] = {0};
            
            sscanf(line.c_str(), "%lx-%lx %s %*x %*x:%*x %*d %s", 
                   &region.start, &region.end, perms, path);
            
            region.permissions = perms;
            region.path = path;
            regions.push_back(region);
        }
        
        return regions;
    }
    
    uintptr_t findLibcBase() {
        auto regions = getMemoryMaps();
        for (const auto& region : regions) {
            if (region.path.find("libc.so") != std::string::npos && 
                region.permissions.find('x') != std::string::npos) {
                return region.start;
            }
        }
        return 0;
    }
    
    uintptr_t findSymbolInLibc(const std::string& symbol) {
        // Get libc base address in target process
        uintptr_t libc_base = findLibcBase();
        if (!libc_base) {
            std::cerr << "Failed to find libc base address" << std::endl;
            return 0;
        }
        
        // Get libc base in our process for symbol resolution
        void* local_libc = dlopen("libc.so.6", RTLD_LAZY);
        if (!local_libc) {
            std::cerr << "Failed to open libc: " << dlerror() << std::endl;
            return 0;
        }
        
        void* local_symbol = dlsym(local_libc, symbol.c_str());
        if (!local_symbol) {
            std::cerr << "Failed to find symbol " << symbol << ": " << dlerror() << std::endl;
            dlclose(local_libc);
            return 0;
        }
        
        // Calculate offset and add to target libc base
        uintptr_t local_libc_base = (uintptr_t)local_libc;
        uintptr_t offset = (uintptr_t)local_symbol - local_libc_base;
        
        dlclose(local_libc);
        return libc_base + offset;
    }
    
    bool writeMemory(uintptr_t address, const void* data, size_t size) {
        const char* bytes = static_cast<const char*>(data);
        for (size_t i = 0; i < size; i += sizeof(long)) {
            long word = 0;
            size_t copy_size = std::min(sizeof(long), size - i);
            memcpy(&word, bytes + i, copy_size);
            
            if (ptrace(PTRACE_POKEDATA, target_pid, address + i, word) == -1) {
                perror("ptrace POKEDATA");
                return false;
            }
        }
        return true;
    }
    
    bool readMemory(uintptr_t address, void* data, size_t size) {
        char* bytes = static_cast<char*>(data);
        for (size_t i = 0; i < size; i += sizeof(long)) {
            long word = ptrace(PTRACE_PEEKDATA, target_pid, address + i, nullptr);
            if (word == -1) {
                perror("ptrace PEEKDATA");
                return false;
            }
            
            size_t copy_size = std::min(sizeof(long), size - i);
            memcpy(bytes + i, &word, copy_size);
        }
        return true;
    }
    
    uintptr_t allocateMemory(size_t size) {
        // Find a suitable memory region for allocation
        auto regions = getMemoryMaps();
        uintptr_t target_addr = 0;
        
        // Look for a gap in memory map
        for (size_t i = 1; i < regions.size(); i++) {
            uintptr_t gap_start = regions[i-1].end;
            uintptr_t gap_end = regions[i].start;
            
            if (gap_end - gap_start >= size) {
                target_addr = gap_start;
                break;
            }
        }
        
        if (!target_addr) {
            std::cerr << "Failed to find suitable memory region" << std::endl;
            return 0;
        }
        
        // Use mmap syscall to allocate memory
        struct user_regs_struct regs, orig_regs;
        if (ptrace(PTRACE_GETREGS, target_pid, nullptr, &orig_regs) == -1) {
            perror("ptrace GETREGS");
            return 0;
        }
        
        regs = orig_regs;
        regs.rax = 9; // mmap syscall number
        regs.rdi = target_addr;
        regs.rsi = size;
        regs.rdx = PROT_READ | PROT_WRITE | PROT_EXEC;
        regs.r10 = MAP_PRIVATE | MAP_ANONYMOUS;
        regs.r8 = -1;
        regs.r9 = 0;
        
        if (ptrace(PTRACE_SETREGS, target_pid, nullptr, &regs) == -1) {
            perror("ptrace SETREGS");
            return 0;
        }
        
        if (ptrace(PTRACE_SINGLESTEP, target_pid, nullptr, nullptr) == -1) {
            perror("ptrace SINGLESTEP");
            return 0;
        }
        
        int status;
        waitpid(target_pid, &status, 0);
        
        if (ptrace(PTRACE_GETREGS, target_pid, nullptr, &regs) == -1) {
            perror("ptrace GETREGS");
            return 0;
        }
        
        uintptr_t allocated_addr = regs.rax;
        
        // Restore original registers
        if (ptrace(PTRACE_SETREGS, target_pid, nullptr, &orig_regs) == -1) {
            perror("ptrace SETREGS restore");
        }
        
        return allocated_addr;
    }

public:
    ProcessInjector(pid_t pid) : target_pid(pid) {}
    
    bool attach() {
        if (ptrace(PTRACE_ATTACH, target_pid, nullptr, nullptr) == -1) {
            perror("ptrace ATTACH");
            return false;
        }
        
        int status;
        waitpid(target_pid, &status, 0);
        return true;
    }
    
    void detach() {
        ptrace(PTRACE_DETACH, target_pid, nullptr, nullptr);
    }
    
    bool injectLibrary(const std::string& library_path) {
        std::cout << "Finding dlopen symbol..." << std::endl;
        uintptr_t dlopen_addr = findSymbolInLibc("dlopen");
        if (!dlopen_addr) {
            std::cerr << "Failed to find dlopen address" << std::endl;
            return false;
        }
        
        std::cout << "dlopen found at: 0x" << std::hex << dlopen_addr << std::dec << std::endl;
        
        // Allocate memory for library path
        size_t path_size = library_path.length() + 1;
        uintptr_t path_addr = allocateMemory(path_size);
        if (!path_addr) {
            std::cerr << "Failed to allocate memory for library path" << std::endl;
            return false;
        }
        
        std::cout << "Allocated memory at: 0x" << std::hex << path_addr << std::dec << std::endl;
        
        // Write library path to allocated memory
        if (!writeMemory(path_addr, library_path.c_str(), path_size)) {
            std::cerr << "Failed to write library path to target process" << std::endl;
            return false;
        }
        
        std::cout << "Library path written to target process" << std::endl;
        
        // Call dlopen
        struct user_regs_struct regs, orig_regs;
        if (ptrace(PTRACE_GETREGS, target_pid, nullptr, &orig_regs) == -1) {
            perror("ptrace GETREGS");
            return false;
        }
        
        regs = orig_regs;
        regs.rip = dlopen_addr;
        regs.rdi = path_addr;        // library path
        regs.rsi = RTLD_LAZY;        // flags
        
        if (ptrace(PTRACE_SETREGS, target_pid, nullptr, &regs) == -1) {
            perror("ptrace SETREGS");
            return false;
        }
        
        std::cout << "Calling dlopen..." << std::endl;
        
        if (ptrace(PTRACE_CONT, target_pid, nullptr, nullptr) == -1) {
            perror("ptrace CONT");
            return false;
        }
        
        int status;
        waitpid(target_pid, &status, 0);
        
        // Check result
        if (ptrace(PTRACE_GETREGS, target_pid, nullptr, &regs) == -1) {
            perror("ptrace GETREGS");
            return false;
        }
        
        if (regs.rax == 0) {
            std::cerr << "dlopen failed (returned NULL)" << std::endl;
            return false;
        }
        
        std::cout << "dlopen succeeded! Library handle: 0x" << std::hex << regs.rax << std::dec << std::endl;
        
        // Restore original registers
        if (ptrace(PTRACE_SETREGS, target_pid, nullptr, &orig_regs) == -1) {
            perror("ptrace SETREGS restore");
        }
        
        return true;
    }
};

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cerr << "Usage: " << argv[0] << " <pid> <library_path>" << std::endl;
        return 1;
    }
    
    pid_t target_pid = std::stoi(argv[1]);
    std::string library_path = argv[2];
    
    std::cout << "CS2 Runtime Injector" << std::endl;
    std::cout << "===================" << std::endl;
    std::cout << "Target PID: " << target_pid << std::endl;
    std::cout << "Library: " << library_path << std::endl;
    std::cout << std::endl;
    
    ProcessInjector injector(target_pid);
    
    std::cout << "Attaching to process..." << std::endl;
    if (!injector.attach()) {
        std::cerr << "Failed to attach to process" << std::endl;
        return 1;
    }
    
    std::cout << "Injecting library..." << std::endl;
    bool success = injector.injectLibrary(library_path);
    
    std::cout << "Detaching from process..." << std::endl;
    injector.detach();
    
    if (success) {
        std::cout << "Injection successful!" << std::endl;
        return 0;
    } else {
        std::cerr << "Injection failed!" << std::endl;
        return 1;
    }
}
