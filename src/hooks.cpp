#include "cheat.h"

// Original function pointers
glXSwapBuffers_t original_glXSwapBuffers = nullptr;

namespace Hooks {
    void Initialize() {
        LOG("Initializing hooks...");
        
        // Get original glXSwapBuffers function
        original_glXSwapBuffers = (glXSwapBuffers_t)dlsym(RTLD_NEXT, "glXSwapBuffers");
        
        if (!original_glXSwapBuffers) {
            LOG_ERROR("Failed to get original glXSwapBuffers function!");
            return;
        }
        
        LOG("Hooks initialized successfully");
    }
    
    void Shutdown() {
        LOG("Shutting down hooks...");
        // Cleanup hook-related resources
    }
}

// Hooked glXSwapBuffers function
extern "C" void glXSwapBuffers(Display* dpy, GLXDrawable drawable) {
    // Call original function first to ensure proper rendering
    if (original_glXSwapBuffers) {
        original_glXSwapBuffers(dpy, drawable);
    }

    // Initialize GUI on first call
    if (!g_initialized) {
        LOG("First frame detected, initializing GUI...");

        // Make sure we have a valid OpenGL context
        GLXContext ctx = glXGetCurrentContext();
        if (ctx && dpy && drawable) {
            // Make context current to ensure proper initialization
            glXMakeCurrent(dpy, drawable, ctx);

            GUI::Initialize();
            g_initialized = true;
            LOG("GUI initialized successfully");
        } else {
            LOG_ERROR("No valid OpenGL context/display/drawable for GUI initialization");
        }
    }

    // Update cheat logic and render GUI
    if (g_initialized) {
        Cheat::Update();

        // Render GUI overlay
        if (g_show_menu) {
            GUI::Render();
        }
    }
}
