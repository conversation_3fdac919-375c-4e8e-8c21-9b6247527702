use clap::Parser;
use std::fs;
use std::path::Path;
use std::process::Command;
use sysinfo::System;

#[derive(Parser)]
#[command(name = "cs2-injector")]
#[command(about = "CS2 library injector and analyzer written in Rust")]
struct Args {
    /// Target process name or PID
    #[arg(short, long, default_value = "cs2")]
    process: String,

    /// Library path to inject
    #[arg(short, long)]
    library: String,

    /// Verbose output
    #[arg(short, long)]
    verbose: bool,
}

fn find_process(name: &str) -> Option<u32> {
    let mut system = System::new_all();
    system.refresh_all();

    // Try to parse as PID first
    if let Ok(pid) = name.parse::<u32>() {
        if system.process(sysinfo::Pid::from(pid as usize)).is_some() {
            return Some(pid);
        }
    }

    // Search by process name
    for (pid, process) in system.processes() {
        if process.name() == name {
            return Some(pid.as_u32());
        }
    }

    None
}

fn analyze_process(pid: u32, verbose: bool) {
    println!("=== Process Analysis ===");
    println!("PID: {}", pid);

    // Check if process exists
    let mut system = System::new_all();
    system.refresh_all();

    if let Some(process) = system.process(sysinfo::Pid::from(pid as usize)) {
        println!("Name: {}", process.name());
        println!("Memory usage: {} KB", process.memory());
        println!("CPU usage: {:.2}%", process.cpu_usage());
    }

    // Check memory maps
    let maps_path = format!("/proc/{}/maps", pid);
    if let Ok(maps_content) = fs::read_to_string(&maps_path) {
        let lines: Vec<&str> = maps_content.lines().collect();
        println!("Memory regions: {}", lines.len());

        if verbose {
            println!("\nLibraries loaded:");
            let mut lib_count = 0;
            for line in &lines {
                if line.contains(".so") && line.contains("r-xp") {
                    if let Some(lib_path) = line.split_whitespace().last() {
                        println!("  {}: {}", lib_count + 1, lib_path);
                        lib_count += 1;
                        if lib_count >= 10 {
                            println!("  ... and {} more", lines.len() - 10);
                            break;
                        }
                    }
                }
            }
        }

        // Check for Steam runtime
        let has_steam = maps_content.contains("steam");
        println!("Steam runtime detected: {}", has_steam);
    }
}

fn check_library(library_path: &str) -> bool {
    if !Path::new(library_path).exists() {
        println!("❌ Library not found: {}", library_path);
        return false;
    }

    println!("✅ Library found: {}", library_path);

    // Check library info
    if let Ok(output) = Command::new("file").arg(library_path).output() {
        let file_info = String::from_utf8_lossy(&output.stdout);
        println!("   Type: {}", file_info.trim());
    }

    // Check dependencies
    if let Ok(output) = Command::new("ldd").arg(library_path).output() {
        let ldd_output = String::from_utf8_lossy(&output.stdout);
        if ldd_output.contains("not found") {
            println!("⚠️  Missing dependencies:");
            for line in ldd_output.lines() {
                if line.contains("not found") {
                    println!("   {}", line.trim());
                }
            }
        } else {
            println!("✅ All dependencies satisfied");
        }
    }

    true
}

fn suggest_injection_methods(library_path: &str, pid: u32) {
    println!("\n=== Injection Methods ===");

    println!("1. **LD_PRELOAD (Recommended)**");
    println!("   Steam launch options:");
    println!("   LD_PRELOAD=\"{}\" %command% -insecure -allow_third_party_software", library_path);
    println!();

    println!("2. **Direct launch**");
    println!("   cd ~/.steam/steam/steamapps/common/Counter-Strike\\ Global\\ Offensive/");
    println!("   LD_PRELOAD=\"{}\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software", library_path);
    println!();

    println!("3. **Bypass Steam runtime**");
    println!("   STEAM_COMPAT_CLIENT_INSTALL_PATH=\"\" LD_PRELOAD=\"{}\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software", library_path);
    println!();

    // Try GDB injection as fallback
    println!("4. **GDB injection (Advanced)**");
    println!("   gdb -batch -ex \"attach {}\" -ex \"call dlopen(\\\"{}\\\", 2)\" -ex \"detach\" -ex \"quit\"", pid, library_path);
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();

    println!("🦀 CS2 Rust Injector");
    println!("====================");

    // Find target process
    let pid = match find_process(&args.process) {
        Some(pid) => {
            println!("✅ Found process: {} (PID: {})", args.process, pid);
            pid
        }
        None => {
            println!("❌ Process not found: {}", args.process);
            println!("Make sure CS2 is running!");
            return Ok(());
        }
    };

    // Analyze process
    analyze_process(pid, args.verbose);

    // Check library
    if !check_library(&args.library) {
        return Ok(());
    }

    // Suggest injection methods
    suggest_injection_methods(&args.library, pid);

    println!("\n🎯 Recommendation: Use the LD_PRELOAD method for best results!");

    Ok(())
}

impl Injector {
    fn new(process_name: &str, verbose: bool) -> Result<Self, InjectionError> {
        let mut system = System::new_all();
        system.refresh_all();
        
        // Find process by name or PID
        let target_pid = if let Ok(pid) = process_name.parse::<i32>() {
            pid
        } else {
            system
                .processes()
                .values()
                .find(|p| p.name() == process_name)
                .map(|p| p.pid().as_u32() as i32)
                .ok_or_else(|| InjectionError::ProcessNotFound(process_name.to_string()))?
        };
        
        let process_name = system
            .process(sysinfo::Pid::from(target_pid as usize))
            .map(|p| p.name().to_string())
            .unwrap_or_else(|| "unknown".to_string());
        
        // Get process memory maps
        let process = Process::new(target_pid)
            .map_err(|e| InjectionError::MemoryError(format!("Failed to access process: {}", e)))?;
        let maps = process.maps()
            .map_err(|e| InjectionError::MemoryError(format!("Failed to get memory maps: {}", e)))?;
        
        if verbose {
            println!("Found process: {} (PID: {})", process_name, target_pid);
            println!("Memory regions: {}", maps.len());
        }
        
        Ok(Injector {
            target: ProcessInfo {
                pid: target_pid,
                name: process_name,
                maps,
            },
            verbose,
        })
    }
    
    fn find_libc_base(&self) -> Result<u64, InjectionError> {
        for map in &self.target.maps {
            if let Some(pathname) = &map.pathname {
                if pathname.contains("libc.so") && map.is_exec() {
                    if self.verbose {
                        println!("Found libc at: 0x{:x}", map.start());
                    }
                    return Ok(map.start());
                }
            }
        }
        Err(InjectionError::SymbolNotFound("libc base".to_string()))
    }
    
    fn get_dlopen_address(&self) -> Result<u64, InjectionError> {
        // This is a simplified approach - in a real implementation,
        // you'd parse the ELF symbols properly
        let libc_base = self.find_libc_base()?;
        
        // Try to find dlopen symbol offset
        // This is a rough estimate - you'd want to parse the actual ELF
        let dlopen_offset = 0x40000; // Approximate offset, varies by system
        
        Ok(libc_base + dlopen_offset)
    }
    
    fn inject_library(&mut self, library_path: &str) -> Result<(), InjectionError> {
        // Verify library exists
        if !Path::new(library_path).exists() {
            return Err(InjectionError::LibraryNotFound(library_path.to_string()));
        }
        
        let pid = Pid::from_raw(self.target.pid);
        
        println!("Attempting injection into process {} (PID: {})", self.target.name, self.target.pid);
        println!("Library: {}", library_path);
        
        // Method 1: Try manual dlopen injection
        if let Err(e) = self.inject_with_dlopen(pid, library_path) {
            println!("dlopen injection failed: {}", e);
            
            // Method 2: Try LD_PRELOAD restart
            println!("Falling back to LD_PRELOAD method...");
            self.suggest_ld_preload(library_path);
        }
        
        Ok(())
    }
    
    fn inject_with_dlopen(&self, pid: Pid, library_path: &str) -> Result<(), InjectionError> {
        println!("Attempting dlopen injection...");
        
        // Attach to process
        ptrace::attach(pid)?;
        waitpid(pid, None)?;
        
        if self.verbose {
            println!("Successfully attached to process");
        }
        
        // Get registers
        let regs = ptrace::getregs(pid)?;
        let original_regs = regs;
        
        if self.verbose {
            println!("Original RIP: 0x{:x}", regs.rip);
        }
        
        // Find dlopen address (simplified)
        let dlopen_addr = self.get_dlopen_address()?;
        
        if self.verbose {
            println!("dlopen address: 0x{:x}", dlopen_addr);
        }
        
        // Allocate memory for library path
        let path_cstring = CString::new(library_path)
            .map_err(|e| InjectionError::MemoryError(format!("Invalid path: {}", e)))?;
        
        // This is where you'd implement the actual memory allocation and dlopen call
        // For now, we'll just demonstrate the concept
        
        println!("Would inject library: {}", library_path);
        println!("Target dlopen address: 0x{:x}", dlopen_addr);
        
        // Detach from process
        ptrace::detach(pid, None)?;
        
        if self.verbose {
            println!("Detached from process");
        }
        
        // For now, return an error to trigger fallback
        Err(InjectionError::MemoryError("Manual injection not fully implemented yet".to_string()))
    }
    
    fn suggest_ld_preload(&self, library_path: &str) {
        println!("\n=== LD_PRELOAD Injection Method ===");
        println!("The manual injection failed. Use LD_PRELOAD instead:");
        println!();
        println!("1. **Steam Launch Options:**");
        println!("   LD_PRELOAD=\"{}\" %command% -insecure -allow_third_party_software", library_path);
        println!();
        println!("2. **Direct Terminal Launch:**");
        println!("   cd ~/.steam/steam/steamapps/common/Counter-Strike\\ Global\\ Offensive/");
        println!("   LD_PRELOAD=\"{}\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software", library_path);
        println!();
        println!("3. **Bypass Steam Runtime:**");
        println!("   STEAM_COMPAT_CLIENT_INSTALL_PATH=\"\" LD_PRELOAD=\"{}\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software", library_path);
        println!();
    }
    
    fn analyze_target(&self) {
        println!("\n=== Target Process Analysis ===");
        println!("Process: {} (PID: {})", self.target.name, self.target.pid);
        println!("Memory regions: {}", self.target.maps.len());
        
        // Analyze loaded libraries
        let mut libraries = Vec::new();
        for map in &self.target.maps {
            if let Some(pathname) = &map.pathname {
                if pathname.contains(".so") && map.is_exec() {
                    libraries.push(pathname.clone());
                }
            }
        }
        
        println!("Loaded libraries: {}", libraries.len());
        if self.verbose {
            for (i, lib) in libraries.iter().take(10).enumerate() {
                println!("  {}: {}", i + 1, lib);
            }
            if libraries.len() > 10 {
                println!("  ... and {} more", libraries.len() - 10);
            }
        }
        
        // Check for Steam runtime
        let has_steam_runtime = libraries.iter().any(|lib| lib.contains("steam"));
        println!("Steam runtime detected: {}", has_steam_runtime);
        
        // Check architecture
        if let Ok(exe_path) = fs::read_link(format!("/proc/{}/exe", self.target.pid)) {
            if let Ok(data) = fs::read(&exe_path) {
                if let Ok(elf) = goblin::elf::Elf::parse(&data) {
                    let arch = match elf.header.e_machine {
                        goblin::elf::header::EM_X86_64 => "x86_64",
                        goblin::elf::header::EM_386 => "i386",
                        _ => "unknown",
                    };
                    let class = if elf.is_64 { "64-bit" } else { "32-bit" };
                    println!("Target architecture: {} {}", class, arch);
                }
            }
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();
    
    println!("CS2 Advanced Injector (Rust)");
    println!("============================");
    
    // Create injector
    let mut injector = Injector::new(&args.process, args.verbose)?;
    
    // Analyze target process
    injector.analyze_target();
    
    // Attempt injection
    injector.inject_library(&args.library)?;
    
    Ok(())
}
