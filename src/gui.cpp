#include "cheat.h"

namespace GUI {
    static bool initialized = false;
    
    void Initialize() {
        if (initialized) return;

        LOG("Initializing ImGui...");

        try {
            // Setup Dear ImGui context
            IMGUI_CHECKVERSION();
            ImGui::CreateContext();
            ImGuiIO& io = ImGui::GetIO(); (void)io;
            io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
            io.ConfigFlags |= ImGuiConfigFlags_NoMouseCursorChange;

            // Disable ini file
            io.IniFilename = nullptr;

            // Setup Dear ImGui style
            ImGui::StyleColorsDark();

            // Make style more visible
            ImGuiStyle& style = ImGui::GetStyle();
            style.Alpha = 0.9f;
            style.WindowRounding = 5.0f;
            style.FrameRounding = 3.0f;

            // Setup Platform/Renderer backends
            // Use OpenGL 3.3 core profile
            const char* glsl_version = "#version 330 core";
            if (!ImGui_ImplOpenGL3_Init(glsl_version)) {
                LOG_ERROR("Failed to initialize ImGui OpenGL3 backend");
                return;
            }

            initialized = true;
            LOG("ImGui initialized successfully with OpenGL3 backend");

        } catch (const std::exception& e) {
            LOG_ERROR("Exception during ImGui initialization: " << e.what());
        } catch (...) {
            LOG_ERROR("Unknown exception during ImGui initialization");
        }
    }
    
    void Shutdown() {
        if (!initialized) return;
        
        LOG("Shutting down ImGui...");
        
        ImGui_ImplOpenGL3_Shutdown();
        ImGui::DestroyContext();
        
        initialized = false;
    }
    
    void Render() {
        if (!initialized) return;
        
        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui::NewFrame();
        
        // Main cheat menu window
        if (g_show_menu) {
            ImGui::Begin("CS2 Internal Cheat", &g_show_menu, ImGuiWindowFlags_AlwaysAutoResize);
            
            ImGui::Text("Welcome to CS2 Internal Cheat!");
            ImGui::Separator();
            
            // Cheat categories
            if (ImGui::CollapsingHeader("Visuals")) {
                static bool esp_enabled = false;
                static bool wallhack_enabled = false;
                
                ImGui::Checkbox("ESP", &esp_enabled);
                ImGui::Checkbox("Wallhack", &wallhack_enabled);
            }
            
            if (ImGui::CollapsingHeader("Aimbot")) {
                static bool aimbot_enabled = false;
                static float aimbot_fov = 90.0f;
                
                ImGui::Checkbox("Enable Aimbot", &aimbot_enabled);
                ImGui::SliderFloat("FOV", &aimbot_fov, 1.0f, 180.0f);
            }
            
            if (ImGui::CollapsingHeader("Misc")) {
                static bool bhop_enabled = false;
                static bool triggerbot_enabled = false;
                
                ImGui::Checkbox("Bunny Hop", &bhop_enabled);
                ImGui::Checkbox("Triggerbot", &triggerbot_enabled);
            }
            
            ImGui::Separator();
            
            // Info section
            ImGui::Text("Status: Injected");
            ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
            
            if (ImGui::Button("Unload Cheat")) {
                g_show_menu = false;
                // Note: Actual unloading would require more complex logic
            }
            
            ImGui::End();
        }
        
        // Render
        ImGui::Render();
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    }
    
    void HandleInput() {
        // Handle input events
        // This would typically be called from input hooks
        // For now, menu toggle is handled elsewhere
    }
}
