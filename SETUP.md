# CS2 Internal Cheat - Linux Setup Guide

## Prerequisites

Install required dependencies:

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential cmake git
sudo apt install libgl1-mesa-dev libx11-dev libgtk-3-dev
sudo apt install pkg-config

# Arch Linux
sudo pacman -S base-devel cmake git
sudo pacman -S mesa libx11 gtk3
sudo pacman -S pkgconf
```

## Building

1. **Clone and build the project:**
   ```bash
   ./build.sh
   ```

   This script will:
   - Download ImGui automatically
   - Check for dependencies
   - Build the cheat library

## Usage

### Method 1: LD_PRELOAD (Recommended)

1. **Add to CS2 launch options in Steam:**
   ```
   LD_PRELOAD="/path/to/cs2-internal/build/bin/libcs2-cheat.so" %command% -insecure -allow_third_party_software
   ```

2. **Start CS2** - the cheat will be automatically loaded

### Method 2: Runtime Injection

1. **Start CS2 normally** with launch options:
   ```
   -insecure -allow_third_party_software
   ```

2. **Run the injection script:**
   ```bash
   ./scripts/inject.sh
   ```

## Features

- **ImGui Menu**: Modern, easy-to-use interface
- **OpenGL Hooking**: Hooks into CS2's rendering pipeline
- **Modular Design**: Easy to extend with new features

### Current Menu Sections:
- **Visuals**: ESP, Wallhack options
- **Aimbot**: Aimbot with FOV settings
- **Misc**: Bunny hop, Triggerbot options

## Important Notes

⚠️ **EDUCATIONAL USE ONLY** ⚠️
- This cheat is for learning purposes only
- Only use in private games with bots
- Always use `-insecure -allow_third_party_software` flags
- This disables VAC and prevents matchmaking

## Troubleshooting

### Build Issues:
- Make sure all dependencies are installed
- Check that you have a C++17 compatible compiler
- Verify OpenGL and X11 development libraries are present

### Runtime Issues:
- Ensure CS2 is running with the required launch flags
- Check that the cheat library was built successfully
- Verify you have OpenGL support on your system

### Menu Not Showing:
- The menu should appear automatically when injected
- Check the console output for initialization messages
- Make sure CS2 is using OpenGL (not Vulkan)

## Development

The project structure:
```
cs2-internal/
├── src/           # Source code
├── external/      # External dependencies (ImGui)
├── scripts/       # Injection and utility scripts
├── build/         # Build output
└── CMakeLists.txt # Build configuration
```

To add new features:
1. Modify the GUI in `src/gui.cpp`
2. Add cheat logic in `src/main.cpp` (Cheat namespace)
3. Add new hooks in `src/hooks.cpp` if needed
