#!/bin/bash

# CS2 Internal Cheat Build Script

echo "CS2 Internal Cheat Build Script"
echo "==============================="

# Create directories
mkdir -p external
mkdir -p build

# Download ImGui if not present
if [ ! -d "external/imgui" ]; then
    echo "Downloading ImGui..."
    cd external
    git clone https://github.com/ocornut/imgui.git
    cd ..
    echo "ImGui downloaded successfully"
else
    echo "ImGui already present"
fi

# Check for required dependencies
echo "Checking dependencies..."

# Check for CMake
if ! command -v cmake &> /dev/null; then
    echo "Error: CMake is not installed!"
    echo "Install with: sudo apt install cmake"
    exit 1
fi

# Check for build essentials
if ! command -v g++ &> /dev/null; then
    echo "Error: g++ is not installed!"
    echo "Install with: sudo apt install build-essential"
    exit 1
fi

# Check for OpenGL development libraries
if ! pkg-config --exists gl; then
    echo "Warning: OpenGL development libraries may not be installed"
    echo "Install with: sudo apt install libgl1-mesa-dev"
fi

# Check for X11 development libraries
if ! pkg-config --exists x11; then
    echo "Warning: X11 development libraries may not be installed"
    echo "Install with: sudo apt install libx11-dev"
fi

echo "Dependencies check complete"

# Build the project
echo "Building project..."
cd build
cmake ..
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo ""
    echo "Build successful!"
    echo "Cheat library created at: build/bin/libcs2-cheat.so"
    echo ""
    echo "To inject the cheat:"
    echo "1. Start CS2 with: LD_PRELOAD=\"$(pwd)/bin/libcs2-cheat.so\" %command% -insecure -allow_third_party_software"
    echo "2. Or use the injection script: ../scripts/inject.sh"
else
    echo "Build failed!"
    exit 1
fi
