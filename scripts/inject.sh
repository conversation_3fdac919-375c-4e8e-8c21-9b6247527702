#!/bin/bash

# CS2 Internal Cheat Injection Script
# Usage: ./inject.sh

CHEAT_LIB="./build/bin/libcs2-cheat.so"
CS2_PROCESS="cs2"

echo "CS2 Internal Cheat Injector"
echo "=========================="

# Check if cheat library exists
if [ ! -f "$CHEAT_LIB" ]; then
    echo "Error: Cheat library not found at $CHEAT_LIB"
    echo "Please build the project first with: mkdir build && cd build && cmake .. && make"
    exit 1
fi

# Check if CS2 is running
if ! pgrep -x "$CS2_PROCESS" > /dev/null; then
    echo "Error: CS2 is not running!"
    echo "Please start CS2 with the following launch options:"
    echo "-insecure -allow_third_party_software"
    exit 1
fi

echo "Found CS2 process: $(pgrep -x $CS2_PROCESS)"
echo "Cheat library: $CHEAT_LIB"
echo ""

# Method 1: LD_PRELOAD injection (restart required)
echo "=== Method 1: LD_PRELOAD (Recommended) ==="
echo "To inject the cheat, restart CS2 with:"
echo "LD_PRELOAD=\"$(realpath $CHEAT_LIB)\" %command%"
echo ""
echo "Add this to CS2's launch options in Steam:"
echo "LD_PRELOAD=\"$(realpath $CHEAT_LIB)\" %command% -insecure -allow_third_party_software"
echo ""

# Method 2: Manual injection using gdb (advanced)
echo "=== Method 2: Runtime Injection (Advanced) ==="
echo "This method injects into a running CS2 process."
echo "WARNING: This may crash the game!"
echo ""
read -p "Do you want to attempt runtime injection? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    CS2_PID=$(pgrep -x $CS2_PROCESS)
    echo "Attempting to inject into PID: $CS2_PID"
    
    # Create GDB script for injection
    cat > /tmp/inject.gdb << EOF
attach $CS2_PID
call dlopen("$(realpath $CHEAT_LIB)", 2)
detach
quit
EOF
    
    # Run injection
    echo "Injecting..."
    gdb -batch -x /tmp/inject.gdb
    
    # Cleanup
    rm /tmp/inject.gdb
    
    if [ $? -eq 0 ]; then
        echo "Injection completed! Check CS2 console for cheat messages."
    else
        echo "Injection failed! Try the LD_PRELOAD method instead."
    fi
else
    echo "Use the LD_PRELOAD method shown above."
fi

echo ""
echo "Once injected, the cheat menu should be visible in-game."
echo "The menu can be toggled with the INSERT key (if input hooks are implemented)."
