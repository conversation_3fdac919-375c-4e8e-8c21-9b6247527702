#!/bin/bash

# CS2 Internal Cheat Injection Script
# Usage: ./inject.sh

CHEAT_LIB="./build/bin/libcs2-cheat.so"
CS2_PROCESS="cs2"
CS2_PATH="/home/<USER>/.steam/steam/steamapps/common/Counter-Strike Global Offensive/game/bin/linuxsteamrt64/cs2"

echo "CS2 Internal Cheat Injector"
echo "=========================="

# Check if cheat library exists
if [ ! -f "$CHEAT_LIB" ]; then
    echo "Error: Cheat library not found at $CHEAT_LIB"
    echo "Please build the project first with: mkdir build && cd build && cmake .. && make"
    exit 1
fi

# Check if CS2 is running
if ! pgrep -x "$CS2_PROCESS" > /dev/null; then
    echo "Error: CS2 is not running!"
    echo "Please start CS2 with the following launch options:"
    echo "-insecure -allow_third_party_software"
    exit 1
fi

echo "Found CS2 process: $(pgrep -x $CS2_PROCESS)"
echo "Cheat library: $CHEAT_LIB"
echo ""

# Method 1: LD_PRELOAD injection (restart required)
echo "=== Method 1: LD_PRELOAD (Recommended) ==="
echo "To inject the cheat, restart CS2 with:"
echo "LD_PRELOAD=\"$(realpath $CHEAT_LIB)\" %command%"
echo ""
echo "Add this to CS2's launch options in Steam:"
echo "LD_PRELOAD=\"$(realpath $CHEAT_LIB)\" %command% -insecure -allow_third_party_software"
echo ""

# Method 2: Runtime injection using custom injector
echo "=== Method 2: Runtime Injection (Advanced) ==="
echo "This method injects into a running CS2 process."
echo "WARNING: This may crash the game!"
echo ""

# Check if injector exists
INJECTOR_PATH="./build/bin/cs2-injector"
if [ ! -f "$INJECTOR_PATH" ]; then
    echo "Error: Injector not found at $INJECTOR_PATH"
    echo "Please build the project first with: ./build.sh"
    exit 1
fi

read -p "Do you want to attempt runtime injection? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    CS2_PID=$(pgrep -x $CS2_PROCESS)
    echo "Attempting to inject into PID: $CS2_PID"
    echo "Using custom injector: $INJECTOR_PATH"
    echo ""

    # Run our custom injector
    echo "Injecting..."
    sudo "$INJECTOR_PATH" "$CS2_PID" "$(realpath $CHEAT_LIB)"

    if [ $? -eq 0 ]; then
        echo ""
        echo "Injection completed! Check CS2 for the cheat menu."
        echo "Look for console messages starting with [CS2-Cheat]"
    else
        echo ""
        echo "Injection failed!"
        echo ""
        echo "Fallback: Trying GDB method..."

        # Fallback to GDB method
        cat > /tmp/inject.gdb << EOF
set confirm off
attach $CS2_PID
call (void*)dlopen("$(realpath $CHEAT_LIB)", 2)
detach
quit
EOF

        gdb -batch -x /tmp/inject.gdb "$CS2_PATH" 2>/dev/null
        rm /tmp/inject.gdb

        if [ $? -eq 0 ]; then
            echo "GDB injection completed! Check CS2 for the cheat menu."
        else
            echo "Both injection methods failed. Try the LD_PRELOAD method instead."
        fi
    fi
else
    echo "Use the LD_PRELOAD method shown above."
fi

echo ""
echo "Once injected, the cheat menu should be visible in-game."
echo "The menu can be toggled with the INSERT key (if input hooks are implemented)."
