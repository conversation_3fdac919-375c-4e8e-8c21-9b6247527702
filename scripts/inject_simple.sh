#!/bin/bash

# Simple CS2 Runtime Injection Script
# Uses a more reliable method with proper error handling

CHEAT_LIB="./build/bin/libcs2-cheat.so"
CS2_PROCESS="cs2"

echo "CS2 Simple Runtime Injector"
echo "==========================="

# Check if cheat library exists
if [ ! -f "$CHEAT_LIB" ]; then
    echo "Error: Cheat library not found at $CHEAT_LIB"
    echo "Please build the project first with: ./build.sh"
    exit 1
fi

# Check if CS2 is running
if ! pgrep -x "$CS2_PROCESS" > /dev/null; then
    echo "Error: CS2 is not running!"
    echo "Please start CS2 with: -insecure -allow_third_party_software"
    exit 1
fi

CS2_PID=$(pgrep -x $CS2_PROCESS)
CHEAT_LIB_FULL=$(realpath "$CHEAT_LIB")

echo "Found CS2 process: $CS2_PID"
echo "Cheat library: $CHEAT_LIB_FULL"
echo ""

# Method 1: Using /proc/pid/mem injection
echo "=== Method 1: /proc/mem injection ==="
echo "This method writes directly to process memory"
echo ""

read -p "Attempt /proc/mem injection? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Creating injection payload..."
    
    # Create a simple C program that calls dlopen
    cat > /tmp/inject_payload.c << 'EOF'
#include <dlfcn.h>
#include <stdio.h>

int main() {
    void* handle = dlopen("LIBRARY_PATH_PLACEHOLDER", RTLD_LAZY);
    if (handle) {
        printf("Library loaded successfully at %p\n", handle);
        return 0;
    } else {
        printf("Failed to load library: %s\n", dlerror());
        return 1;
    }
}
EOF
    
    # Replace placeholder with actual library path
    sed -i "s|LIBRARY_PATH_PLACEHOLDER|$CHEAT_LIB_FULL|g" /tmp/inject_payload.c
    
    # Compile the payload
    gcc -o /tmp/inject_payload /tmp/inject_payload.c -ldl
    
    if [ $? -eq 0 ]; then
        echo "Payload compiled successfully"
        echo "Executing payload in target process context..."
        
        # Use gdb to execute our payload
        gdb -batch -ex "set confirm off" \
            -ex "attach $CS2_PID" \
            -ex "call (int)system(\"/tmp/inject_payload\")" \
            -ex "detach" \
            -ex "quit" 2>/dev/null
        
        # Cleanup
        rm -f /tmp/inject_payload /tmp/inject_payload.c
        
        echo "Injection attempt completed!"
    else
        echo "Failed to compile payload"
        rm -f /tmp/inject_payload.c
    fi
fi

echo ""
echo "=== Method 2: LD_PRELOAD with process restart ==="
echo "This method requires restarting CS2 but is most reliable"
echo ""

read -p "Show LD_PRELOAD instructions? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "1. Close CS2 completely"
    echo "2. Add this to CS2's Steam launch options:"
    echo "   LD_PRELOAD=\"$CHEAT_LIB_FULL\" %command% -insecure -allow_third_party_software"
    echo "3. Start CS2 - the cheat will load automatically"
    echo ""
    echo "Alternative: Run CS2 from terminal:"
    echo "   LD_PRELOAD=\"$CHEAT_LIB_FULL\" steam steam://rungameid/730 -insecure -allow_third_party_software"
fi

echo ""
echo "=== Method 3: Manual GDB injection (Advanced) ==="
echo "Direct dlopen call with better error handling"
echo ""

read -p "Attempt manual GDB injection? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Creating GDB script..."
    
    cat > /tmp/manual_inject.gdb << EOF
set confirm off
set pagination off
attach $CS2_PID

# Print current working directory
call (char*)getcwd(0, 0)

# Try to load the library
set \$handle = (void*)dlopen("$CHEAT_LIB_FULL", 2)
printf "dlopen returned: %p\n", \$handle

# If dlopen failed, print error
if (\$handle == 0)
    call (char*)dlerror()
    printf "dlopen failed: %s\n", \$
else
    printf "Library loaded successfully!\n"
end

detach
quit
EOF
    
    echo "Running GDB injection..."
    gdb -batch -x /tmp/manual_inject.gdb 2>/dev/null
    
    # Cleanup
    rm -f /tmp/manual_inject.gdb
    
    echo "Manual injection completed!"
fi

echo ""
echo "If injection was successful, you should see:"
echo "- Console messages starting with [CS2-Cheat]"
echo "- An ImGui menu overlay in the game"
echo ""
echo "If no menu appears, the library may not be compatible with"
echo "the current CS2 version or OpenGL context."
