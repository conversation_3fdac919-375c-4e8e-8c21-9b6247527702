#!/bin/bash

# Direct injection test for CS2

CS2_PID=$(pgrep -x cs2)
CHEAT_LIB="$(realpath ./build/bin/libcs2-cheat.so)"

if [ -z "$CS2_PID" ]; then
    echo "CS2 is not running!"
    exit 1
fi

echo "Testing injection into CS2 PID: $CS2_PID"
echo "Library: $CHEAT_LIB"
echo ""

# Method 1: Simple GDB dlopen
echo "=== Testing GDB dlopen ==="
gdb -batch \
    -ex "set confirm off" \
    -ex "attach $CS2_PID" \
    -ex "call (void*)dlopen(\"$CHEAT_LIB\", 2)" \
    -ex "call (char*)dlerror()" \
    -ex "detach" \
    -ex "quit" 2>/dev/null

echo ""

# Method 2: Check if library is loaded
echo "=== Checking loaded libraries ==="
echo "Looking for our library in process memory maps..."
if grep -q "libcs2-cheat.so" /proc/$CS2_PID/maps; then
    echo "✓ Library is loaded in process memory!"
    echo "Memory regions:"
    grep "libcs2-cheat.so" /proc/$CS2_PID/maps
else
    echo "✗ Library not found in process memory"
fi

echo ""

# Method 3: Check process file descriptors
echo "=== Checking file descriptors ==="
if ls -la /proc/$CS2_PID/fd/ | grep -q "libcs2-cheat.so"; then
    echo "✓ Library file descriptor found"
else
    echo "✗ No library file descriptor found"
fi

echo ""
echo "If the library is loaded, check CS2 console for [CS2-Cheat] messages"
echo "The ImGui menu should appear as an overlay in the game"
