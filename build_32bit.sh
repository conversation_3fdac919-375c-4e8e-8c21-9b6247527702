#!/bin/bash

# Build 32-bit version of CS2 cheat

echo "Building 32-bit CS2 Cheat"
echo "========================="

# Check if 32-bit development tools are available
echo "=== Checking 32-bit development environment ==="

# Check for 32-bit gcc
if ! gcc -m32 -v &>/dev/null; then
    echo "✗ 32-bit GCC not available"
    echo "Install with: sudo apt install gcc-multilib g++-multilib"
    echo "Or on Arch: sudo pacman -S lib32-gcc-libs"
    exit 1
else
    echo "✓ 32-bit GCC available"
fi

# Check for 32-bit libraries
echo "Checking for 32-bit OpenGL libraries..."
if [ -f "/usr/lib32/libGL.so" ] || [ -f "/usr/lib/i386-linux-gnu/libGL.so.1" ]; then
    echo "✓ 32-bit OpenGL libraries found"
else
    echo "⚠ 32-bit OpenGL libraries may be missing"
    echo "Install with: sudo apt install libgl1-mesa-dev:i386"
fi

# Create 32-bit build directory
mkdir -p build32
cd build32

echo ""
echo "=== Building minimal 32-bit cheat ==="

# Create minimal 32-bit cheat
cat > minimal_cheat_32.c << 'EOF'
#include <stdio.h>
#include <dlfcn.h>
#include <GL/gl.h>
#include <GL/glx.h>
#include <X11/Xlib.h>

// Global state
static void (*original_glXSwapBuffers)(Display*, GLXDrawable) = NULL;
static int frame_count = 0;
static int cheat_initialized = 0;

// Simple cheat menu state
static int show_menu = 1;
static int esp_enabled = 0;
static int aimbot_enabled = 0;

__attribute__((constructor))
void cheat_load() {
    printf("[CS2-CHEAT-32] CS2 Internal Cheat (32-bit) loaded!\n");
    printf("[CS2-CHEAT-32] Initializing hooks...\n");
    fflush(stdout);
    
    // Get original glXSwapBuffers
    original_glXSwapBuffers = (void(*)(Display*, GLXDrawable))dlsym(RTLD_NEXT, "glXSwapBuffers");
    if (!original_glXSwapBuffers) {
        printf("[CS2-CHEAT-32] ERROR: Failed to get original glXSwapBuffers\n");
        fflush(stdout);
    } else {
        printf("[CS2-CHEAT-32] Hooks initialized successfully\n");
        fflush(stdout);
    }
}

// Simple console-based "menu"
void update_cheat() {
    frame_count++;
    
    // Print menu every 300 frames (roughly every 5 seconds at 60fps)
    if (frame_count % 300 == 0) {
        printf("\n[CS2-CHEAT-32] === CHEAT MENU ===\n");
        printf("[CS2-CHEAT-32] Frame: %d\n", frame_count);
        printf("[CS2-CHEAT-32] ESP: %s\n", esp_enabled ? "ON" : "OFF");
        printf("[CS2-CHEAT-32] Aimbot: %s\n", aimbot_enabled ? "ON" : "OFF");
        printf("[CS2-CHEAT-32] Menu: %s\n", show_menu ? "VISIBLE" : "HIDDEN");
        printf("[CS2-CHEAT-32] ==================\n");
        fflush(stdout);
        
        // Toggle features for demo (in real cheat, this would be input-driven)
        if (frame_count % 600 == 0) {
            esp_enabled = !esp_enabled;
            printf("[CS2-CHEAT-32] Toggled ESP: %s\n", esp_enabled ? "ON" : "OFF");
            fflush(stdout);
        }
    }
}

// Hooked glXSwapBuffers function
void glXSwapBuffers(Display* dpy, GLXDrawable drawable) {
    // Initialize on first call
    if (!cheat_initialized) {
        printf("[CS2-CHEAT-32] First frame detected! Cheat is active.\n");
        printf("[CS2-CHEAT-32] OpenGL context available\n");
        fflush(stdout);
        cheat_initialized = 1;
    }
    
    // Update cheat logic
    update_cheat();
    
    // Call original function
    if (original_glXSwapBuffers) {
        original_glXSwapBuffers(dpy, drawable);
    }
}

__attribute__((destructor))
void cheat_unload() {
    printf("[CS2-CHEAT-32] CS2 Internal Cheat unloading...\n");
    printf("[CS2-CHEAT-32] Total frames processed: %d\n", frame_count);
    printf("[CS2-CHEAT-32] Cleanup complete.\n");
    fflush(stdout);
}
EOF

echo "Compiling 32-bit cheat library..."

# Compile 32-bit version
gcc -m32 -shared -fPIC -o libcs2-cheat-32.so minimal_cheat_32.c -ldl

if [ $? -eq 0 ]; then
    echo "✓ 32-bit cheat compiled successfully!"
    echo ""
    echo "Library info:"
    file libcs2-cheat-32.so
    echo ""
    echo "ELF header:"
    readelf -h libcs2-cheat-32.so | grep -E "(Class|Machine)"
    echo ""
    
    CHEAT_32_PATH="$(pwd)/libcs2-cheat-32.so"
    echo "32-bit cheat library: $CHEAT_32_PATH"
    echo ""
    echo "=== Test Commands ==="
    echo ""
    echo "1. Direct launch:"
    echo "   cd ~/.steam/steam/steamapps/common/Counter-Strike\\ Global\\ Offensive/"
    echo "   LD_PRELOAD=\"$CHEAT_32_PATH\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software"
    echo ""
    echo "2. Steam launch options:"
    echo "   LD_PRELOAD=\"$CHEAT_32_PATH\" %command% -insecure -allow_third_party_software"
    echo ""
    echo "3. Test with simple command first:"
    echo "   LD_PRELOAD=\"$CHEAT_32_PATH\" /bin/true"
    
else
    echo "✗ Failed to compile 32-bit cheat"
    echo ""
    echo "Possible solutions:"
    echo "1. Install 32-bit development libraries:"
    echo "   sudo apt install gcc-multilib g++-multilib libc6-dev-i386"
    echo "   sudo apt install libgl1-mesa-dev:i386 libx11-dev:i386"
    echo ""
    echo "2. On Arch Linux:"
    echo "   sudo pacman -S lib32-gcc-libs lib32-glibc"
    echo "   Enable multilib repository in /etc/pacman.conf"
fi

# Cleanup
rm -f minimal_cheat_32.c

cd ..
echo ""
echo "Build complete. Check build32/ directory for output."
