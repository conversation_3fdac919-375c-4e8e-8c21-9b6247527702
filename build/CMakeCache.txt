# This is the CMakeCache file.
# For build in directory: /home/<USER>/Desktop/cs2-internal/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/Desktop/cs2-internal/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=cs2-internal

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
FREETYPE_INCLUDE_DIR_freetype2:PATH=/usr/include/freetype2

//Path to a file.
FREETYPE_INCLUDE_DIR_ft2build:PATH=/usr/include/freetype2

//Path to a library.
FREETYPE_LIBRARY_DEBUG:FILEPATH=FREETYPE_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FREETYPE_LIBRARY_RELEASE:FILEPATH=/usr/lib/libfreetype.so

//Path to a file.
Fontconfig_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Fontconfig_LIBRARY:FILEPATH=/usr/lib/libfontconfig.so

//Path to a file.
OPENGL_EGL_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES2_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES3_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLU_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLX_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENGL_egl_LIBRARY:FILEPATH=/usr/lib/libEGL.so

//Path to a library.
OPENGL_gles2_LIBRARY:FILEPATH=/usr/lib/libGLESv2.so

//Path to a library.
OPENGL_gles3_LIBRARY:FILEPATH=/usr/lib/libGLESv2.so

//Path to a library.
OPENGL_glu_LIBRARY:FILEPATH=/usr/lib/libGLU.so

//Path to a library.
OPENGL_glx_LIBRARY:FILEPATH=/usr/lib/libGLX.so

//Path to a library.
OPENGL_opengl_LIBRARY:FILEPATH=/usr/lib/libOpenGL.so

//Path to a file.
OPENGL_xmesa_INCLUDE_DIR:PATH=OPENGL_xmesa_INCLUDE_DIR-NOTFOUND

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a file.
X11_ICE_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_ICE_LIB:FILEPATH=/usr/lib/libICE.so

//Path to a file.
X11_SM_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_SM_LIB:FILEPATH=/usr/lib/libSM.so

//Path to a file.
X11_X11_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_X11_LIB:FILEPATH=/usr/lib/libX11.so

//Path to a file.
X11_X11_xcb_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_X11_xcb_LIB:FILEPATH=/usr/lib/libX11-xcb.so

//Path to a file.
X11_XRes_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_XRes_LIB:FILEPATH=/usr/lib/libXRes.so

//Path to a file.
X11_XShm_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_XSync_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xaccessrules_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xaccessstr_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xau_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xau_LIB:FILEPATH=/usr/lib/libXau.so

//Path to a file.
X11_Xaw_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xaw_LIB:FILEPATH=/usr/lib/libXaw.so

//Path to a file.
X11_Xcomposite_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xcomposite_LIB:FILEPATH=/usr/lib/libXcomposite.so

//Path to a file.
X11_Xcursor_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xcursor_LIB:FILEPATH=/usr/lib/libXcursor.so

//Path to a file.
X11_Xdamage_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xdamage_LIB:FILEPATH=/usr/lib/libXdamage.so

//Path to a file.
X11_Xdbe_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xdmcp_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xdmcp_LIB:FILEPATH=/usr/lib/libXdmcp.so

//Path to a file.
X11_Xext_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xext_LIB:FILEPATH=/usr/lib/libXext.so

//Path to a file.
X11_Xfixes_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xfixes_LIB:FILEPATH=/usr/lib/libXfixes.so

//Path to a file.
X11_Xft_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xft_LIB:FILEPATH=/usr/lib/libXft.so

//Path to a file.
X11_Xi_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xi_LIB:FILEPATH=/usr/lib/libXi.so

//Path to a file.
X11_Xinerama_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xinerama_LIB:FILEPATH=/usr/lib/libXinerama.so

//Path to a file.
X11_Xkb_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xkblib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xlib_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xmu_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xmu_LIB:FILEPATH=/usr/lib/libXmu.so

//Path to a file.
X11_Xpm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xpm_LIB:FILEPATH=/usr/lib/libXpm.so

//Path to a file.
X11_Xpresent_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xpresent_LIB:FILEPATH=/usr/lib/libXpresent.so

//Path to a file.
X11_Xrandr_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrandr_LIB:FILEPATH=/usr/lib/libXrandr.so

//Path to a file.
X11_Xrender_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xrender_LIB:FILEPATH=/usr/lib/libXrender.so

//Path to a file.
X11_Xshape_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xss_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xss_LIB:FILEPATH=/usr/lib/libXss.so

//Path to a file.
X11_Xt_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xt_LIB:FILEPATH=/usr/lib/libXt.so

//Path to a file.
X11_Xtst_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xtst_LIB:FILEPATH=/usr/lib/libXtst.so

//Path to a file.
X11_Xutil_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_Xv_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xv_LIB:FILEPATH=/usr/lib/libXv.so

//Path to a file.
X11_Xxf86misc_INCLUDE_PATH:PATH=X11_Xxf86misc_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_Xxf86misc_LIB:FILEPATH=X11_Xxf86misc_LIB-NOTFOUND

//Path to a file.
X11_Xxf86vm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_Xxf86vm_LIB:FILEPATH=/usr/lib/libXxf86vm.so

//Path to a file.
X11_dpms_INCLUDE_PATH:PATH=/usr/include

//Path to a file.
X11_xcb_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_LIB:FILEPATH=/usr/lib/libxcb.so

//Path to a file.
X11_xcb_composite_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_composite_LIB:FILEPATH=/usr/lib/libxcb-composite.so

//Path to a file.
X11_xcb_cursor_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_cursor_LIB:FILEPATH=/usr/lib/libxcb-cursor.so

//Path to a file.
X11_xcb_damage_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_damage_LIB:FILEPATH=/usr/lib/libxcb-damage.so

//Path to a file.
X11_xcb_dpms_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_dpms_LIB:FILEPATH=/usr/lib/libxcb-dpms.so

//Path to a file.
X11_xcb_dri2_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_dri2_LIB:FILEPATH=/usr/lib/libxcb-dri2.so

//Path to a file.
X11_xcb_dri3_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_dri3_LIB:FILEPATH=/usr/lib/libxcb-dri3.so

//Path to a file.
X11_xcb_errors_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_errors_LIB:FILEPATH=/usr/lib/libxcb-errors.so

//Path to a file.
X11_xcb_ewmh_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_ewmh_LIB:FILEPATH=/usr/lib/libxcb-ewmh.so

//Path to a file.
X11_xcb_glx_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_glx_LIB:FILEPATH=/usr/lib/libxcb-glx.so

//Path to a file.
X11_xcb_icccm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_icccm_LIB:FILEPATH=/usr/lib/libxcb-icccm.so

//Path to a file.
X11_xcb_image_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_image_LIB:FILEPATH=/usr/lib/libxcb-image.so

//Path to a file.
X11_xcb_keysyms_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_keysyms_LIB:FILEPATH=/usr/lib/libxcb-keysyms.so

//Path to a file.
X11_xcb_present_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_present_LIB:FILEPATH=/usr/lib/libxcb-present.so

//Path to a file.
X11_xcb_randr_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_randr_LIB:FILEPATH=/usr/lib/libxcb-randr.so

//Path to a file.
X11_xcb_record_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_record_LIB:FILEPATH=/usr/lib/libxcb-record.so

//Path to a file.
X11_xcb_render_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_render_LIB:FILEPATH=/usr/lib/libxcb-render.so

//Path to a file.
X11_xcb_render_util_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_render_util_LIB:FILEPATH=/usr/lib/libxcb-render-util.so

//Path to a file.
X11_xcb_res_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_res_LIB:FILEPATH=/usr/lib/libxcb-res.so

//Path to a file.
X11_xcb_screensaver_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_screensaver_LIB:FILEPATH=/usr/lib/libxcb-screensaver.so

//Path to a file.
X11_xcb_shape_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_shape_LIB:FILEPATH=/usr/lib/libxcb-shape.so

//Path to a file.
X11_xcb_shm_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_shm_LIB:FILEPATH=/usr/lib/libxcb-shm.so

//Path to a file.
X11_xcb_sync_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_sync_LIB:FILEPATH=/usr/lib/libxcb-sync.so

//Path to a file.
X11_xcb_util_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_util_LIB:FILEPATH=/usr/lib/libxcb-util.so

//Path to a file.
X11_xcb_xf86dri_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xf86dri_LIB:FILEPATH=/usr/lib/libxcb-xf86dri.so

//Path to a file.
X11_xcb_xfixes_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xfixes_LIB:FILEPATH=/usr/lib/libxcb-xfixes.so

//Path to a file.
X11_xcb_xinerama_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xinerama_LIB:FILEPATH=/usr/lib/libxcb-xinerama.so

//Path to a file.
X11_xcb_xinput_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xinput_LIB:FILEPATH=/usr/lib/libxcb-xinput.so

//Path to a file.
X11_xcb_xkb_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xkb_LIB:FILEPATH=/usr/lib/libxcb-xkb.so

//Path to a file.
X11_xcb_xrm_INCLUDE_PATH:PATH=X11_xcb_xrm_INCLUDE_PATH-NOTFOUND

//Path to a library.
X11_xcb_xrm_LIB:FILEPATH=X11_xcb_xrm_LIB-NOTFOUND

//Path to a file.
X11_xcb_xtest_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xtest_LIB:FILEPATH=/usr/lib/libxcb-xtest.so

//Path to a file.
X11_xcb_xv_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xv_LIB:FILEPATH=/usr/lib/libxcb-xv.so

//Path to a file.
X11_xcb_xvmc_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xcb_xvmc_LIB:FILEPATH=/usr/lib/libxcb-xvmc.so

//Path to a file.
X11_xkbcommon_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xkbcommon_LIB:FILEPATH=/usr/lib/libxkbcommon.so

//Path to a file.
X11_xkbcommon_X11_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xkbcommon_X11_LIB:FILEPATH=/usr/lib/libxkbcommon-x11.so

//Path to a file.
X11_xkbfile_INCLUDE_PATH:PATH=/usr/include

//Path to a library.
X11_xkbfile_LIB:FILEPATH=/usr/lib/libxkbfile.so

//Value Computed by CMake
cs2-internal_BINARY_DIR:STATIC=/home/<USER>/Desktop/cs2-internal/build

//Value Computed by CMake
cs2-internal_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
cs2-internal_SOURCE_DIR:STATIC=/home/<USER>/Desktop/cs2-internal

//Path to a library.
pkgcfg_lib_GTK3_atk-1.0:FILEPATH=/usr/lib/libatk-1.0.so

//Path to a library.
pkgcfg_lib_GTK3_cairo:FILEPATH=/usr/lib/libcairo.so

//Path to a library.
pkgcfg_lib_GTK3_cairo-gobject:FILEPATH=/usr/lib/libcairo-gobject.so

//Path to a library.
pkgcfg_lib_GTK3_gdk-3:FILEPATH=/usr/lib/libgdk-3.so

//Path to a library.
pkgcfg_lib_GTK3_gdk_pixbuf-2.0:FILEPATH=/usr/lib/libgdk_pixbuf-2.0.so

//Path to a library.
pkgcfg_lib_GTK3_gio-2.0:FILEPATH=/usr/lib/libgio-2.0.so

//Path to a library.
pkgcfg_lib_GTK3_glib-2.0:FILEPATH=/usr/lib/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GTK3_gobject-2.0:FILEPATH=/usr/lib/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_GTK3_gtk-3:FILEPATH=/usr/lib/libgtk-3.so

//Path to a library.
pkgcfg_lib_GTK3_harfbuzz:FILEPATH=/usr/lib/libharfbuzz.so

//Path to a library.
pkgcfg_lib_GTK3_pango-1.0:FILEPATH=/usr/lib/libpango-1.0.so

//Path to a library.
pkgcfg_lib_GTK3_pangocairo-1.0:FILEPATH=/usr/lib/libpangocairo-1.0.so

//Path to a library.
pkgcfg_lib_GTK3_z:FILEPATH=/usr/lib/libz.so

//Path to a library.
pkgcfg_lib_PKG_FONTCONFIG_fontconfig:FILEPATH=/usr/lib/libfontconfig.so


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Desktop/cs2-internal/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have function connect
CMAKE_HAVE_CONNECT:INTERNAL=1
//Have function gethostbyname
CMAKE_HAVE_GETHOSTBYNAME:INTERNAL=1
//Have function remove
CMAKE_HAVE_REMOVE:INTERNAL=1
//Have function shmat
CMAKE_HAVE_SHMAT:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Desktop/cs2-internal
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//Have library ICE
CMAKE_LIB_ICE_HAS_ICECONNECTIONNUMBER:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/usr/lib/libOpenGL.so][/usr/lib/libGLX.so][/usr/include][ ][v()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v2.5.1()]
//Details about finding X11
FIND_PACKAGE_MESSAGE_DETAILS_X11:INTERNAL=[/usr/include][/usr/lib/libX11.so][ ][v()]
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_freetype2
FREETYPE_INCLUDE_DIR_freetype2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_ft2build
FREETYPE_INCLUDE_DIR_ft2build-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_DEBUG
FREETYPE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_RELEASE
FREETYPE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_INCLUDE_DIR
Fontconfig_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Fontconfig_LIBRARY
Fontconfig_LIBRARY-ADVANCED:INTERNAL=1
GTK3_CFLAGS:INTERNAL=-I/usr/include/gtk-3.0;-I/usr/include/pango-1.0;-I/usr/include;-I/usr/include/cloudproviders;-I/usr/include/cairo;-I/usr/include/gdk-pixbuf-2.0;-I/usr/include/at-spi2-atk/2.0;-I/usr/include/at-spi-2.0;-I/usr/include/atk-1.0;-I/usr/include/dbus-1.0;-I/usr/lib/dbus-1.0/include;-I/usr/include/fribidi;-I/usr/include/pixman-1;-I/usr/include/harfbuzz;-I/usr/include/freetype2;-I/usr/include/libpng16;-I/usr/include/gio-unix-2.0;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-DWITH_GZFILEOP;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread
GTK3_CFLAGS_I:INTERNAL=
GTK3_CFLAGS_OTHER:INTERNAL=-DWITH_GZFILEOP;-pthread
GTK3_FOUND:INTERNAL=1
GTK3_INCLUDEDIR:INTERNAL=/usr/include
GTK3_INCLUDE_DIRS:INTERNAL=/usr/include/gtk-3.0;/usr/include/pango-1.0;/usr/include;/usr/include/cloudproviders;/usr/include/cairo;/usr/include/gdk-pixbuf-2.0;/usr/include/at-spi2-atk/2.0;/usr/include/at-spi-2.0;/usr/include/atk-1.0;/usr/include/dbus-1.0;/usr/lib/dbus-1.0/include;/usr/include/fribidi;/usr/include/pixman-1;/usr/include/harfbuzz;/usr/include/freetype2;/usr/include/libpng16;/usr/include/gio-unix-2.0;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GTK3_LDFLAGS:INTERNAL=-L/usr/lib;-lgtk-3;-lgdk-3;-lz;-lpangocairo-1.0;-lcairo-gobject;-lgdk_pixbuf-2.0;-latk-1.0;-lpango-1.0;-lcairo;-lharfbuzz;-lgio-2.0;-lgobject-2.0;-lglib-2.0
GTK3_LDFLAGS_OTHER:INTERNAL=
GTK3_LIBDIR:INTERNAL=/usr/lib
GTK3_LIBRARIES:INTERNAL=gtk-3;gdk-3;z;pangocairo-1.0;cairo-gobject;gdk_pixbuf-2.0;atk-1.0;pango-1.0;cairo;harfbuzz;gio-2.0;gobject-2.0;glib-2.0
GTK3_LIBRARY_DIRS:INTERNAL=/usr/lib
GTK3_LIBS:INTERNAL=
GTK3_LIBS_L:INTERNAL=
GTK3_LIBS_OTHER:INTERNAL=
GTK3_LIBS_PATHS:INTERNAL=
GTK3_MODULE_NAME:INTERNAL=gtk+-3.0
GTK3_PREFIX:INTERNAL=/usr
GTK3_STATIC_CFLAGS:INTERNAL=-I/usr/include/gtk-3.0;-I/usr/include/pango-1.0;-I/usr/include;-I/usr/include/cloudproviders;-I/usr/include/cairo;-I/usr/include/gdk-pixbuf-2.0;-I/usr/include/at-spi2-atk/2.0;-I/usr/include/at-spi-2.0;-I/usr/include/atk-1.0;-I/usr/include/dbus-1.0;-I/usr/lib/dbus-1.0/include;-I/usr/include/fribidi;-I/usr/include/pixman-1;-I/usr/include/harfbuzz;-I/usr/include/freetype2;-I/usr/include/libpng16;-I/usr/include/gio-unix-2.0;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-DWITH_GZFILEOP;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread;-DLZMA_API_STATIC;-DXML_STATIC
GTK3_STATIC_CFLAGS_I:INTERNAL=
GTK3_STATIC_CFLAGS_OTHER:INTERNAL=-DWITH_GZFILEOP;-pthread;-DLZMA_API_STATIC;-DXML_STATIC
GTK3_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/gtk-3.0;/usr/include/pango-1.0;/usr/include;/usr/include/cloudproviders;/usr/include/cairo;/usr/include/gdk-pixbuf-2.0;/usr/include/at-spi2-atk/2.0;/usr/include/at-spi-2.0;/usr/include/atk-1.0;/usr/include/dbus-1.0;/usr/lib/dbus-1.0/include;/usr/include/fribidi;/usr/include/pixman-1;/usr/include/harfbuzz;/usr/include/freetype2;/usr/include/libpng16;/usr/include/gio-unix-2.0;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GTK3_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lgtk-3;-lgdk-3;-lz;-lpangocairo-1.0;-lm;-lXrandr;-lXcursor;-lXcomposite;-lXdamage;-lXinerama;-lcloudproviders;-lcairo-gobject;-lm;-ldl;-lgdk_pixbuf-2.0;-lm;-ltiff;-ljbig;-lz;-lm;-lzstd;-llzma;-pthread;-lpthread;-ljpeg;-latk-bridge-2.0;-latspi;-lXtst;-lXi;-lXfixes;-latk-1.0;-ldbus-1;-pthread;-lsystemd;-Wl,--export-dynamic;-lxkbcommon;-lwayland-cursor;-lwayland-egl;-lwayland-client;-lm;-pthread;-lrt;-lepoxy;-ldl;-lGL;-lEGL;-lpangoft2-1.0;-lm;-lpango-1.0;-lm;-lfribidi;-lthai;-ldatrie;-lXft;-lcairo;-lm;-ldl;-lfontconfig;-pthread;-lm;-lexpat;-lm;-lXext;-lXrender;-lX11;-lpthread;-lxcb-render;-lxcb-shm;-lxcb;-lXau;-lXdmcp;-lpixman-1;-lm;-pthread;-lharfbuzz-gobject;-lharfbuzz;-lm;-lfreetype;-lbz2;-lpng16;-lm;-lm;-lbrotlidec;-lbrotlicommon;-lgraphite2;-lgio-2.0;-lgobject-2.0;-lffi;-lgmodule-2.0;-pthread;-lglib-2.0;-lm;-lpcre2-8;-lz;-lmount;-lblkid;-lsysprof-capture-4;-pthread
GTK3_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-Wl,--export-dynamic;-pthread;-pthread;-pthread;-pthread;-pthread
GTK3_STATIC_LIBDIR:INTERNAL=
GTK3_STATIC_LIBRARIES:INTERNAL=gtk-3;gdk-3;z;pangocairo-1.0;m;Xrandr;Xcursor;Xcomposite;Xdamage;Xinerama;cloudproviders;cairo-gobject;m;dl;gdk_pixbuf-2.0;m;tiff;jbig;z;m;zstd;lzma;pthread;jpeg;atk-bridge-2.0;atspi;Xtst;Xi;Xfixes;atk-1.0;dbus-1;systemd;xkbcommon;wayland-cursor;wayland-egl;wayland-client;m;rt;epoxy;dl;GL;EGL;pangoft2-1.0;m;pango-1.0;m;fribidi;thai;datrie;Xft;cairo;m;dl;fontconfig;m;expat;m;Xext;Xrender;X11;pthread;xcb-render;xcb-shm;xcb;Xau;Xdmcp;pixman-1;m;harfbuzz-gobject;harfbuzz;m;freetype;bz2;png16;m;m;brotlidec;brotlicommon;graphite2;gio-2.0;gobject-2.0;ffi;gmodule-2.0;glib-2.0;m;pcre2-8;z;mount;blkid;sysprof-capture-4
GTK3_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
GTK3_STATIC_LIBS:INTERNAL=
GTK3_STATIC_LIBS_L:INTERNAL=
GTK3_STATIC_LIBS_OTHER:INTERNAL=
GTK3_STATIC_LIBS_PATHS:INTERNAL=
GTK3_VERSION:INTERNAL=3.24.49
GTK3_gtk+-3.0_INCLUDEDIR:INTERNAL=
GTK3_gtk+-3.0_LIBDIR:INTERNAL=
GTK3_gtk+-3.0_PREFIX:INTERNAL=
GTK3_gtk+-3.0_VERSION:INTERNAL=
//ADVANCED property for variable: OPENGL_EGL_INCLUDE_DIR
OPENGL_EGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLES2_INCLUDE_DIR
OPENGL_GLES2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLES3_INCLUDE_DIR
OPENGL_GLES3_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLU_INCLUDE_DIR
OPENGL_GLU_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLX_INCLUDE_DIR
OPENGL_GLX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_egl_LIBRARY
OPENGL_egl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles2_LIBRARY
OPENGL_gles2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles3_LIBRARY
OPENGL_gles3_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glx_LIBRARY
OPENGL_glx_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_opengl_LIBRARY
OPENGL_opengl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_xmesa_INCLUDE_DIR
OPENGL_xmesa_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
PKG_FONTCONFIG_CFLAGS:INTERNAL=-I/usr/include;-I/usr/include/freetype2;-I/usr/include/libpng16;-DWITH_GZFILEOP;-I/usr/include/harfbuzz;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/sysprof-6;-pthread
PKG_FONTCONFIG_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_CFLAGS_OTHER:INTERNAL=-DWITH_GZFILEOP;-pthread
PKG_FONTCONFIG_FOUND:INTERNAL=1
PKG_FONTCONFIG_INCLUDEDIR:INTERNAL=/usr/include
PKG_FONTCONFIG_INCLUDE_DIRS:INTERNAL=/usr/include;/usr/include/freetype2;/usr/include/libpng16;/usr/include/harfbuzz;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/sysprof-6
PKG_FONTCONFIG_LDFLAGS:INTERNAL=-L/usr/lib;-lfontconfig
PKG_FONTCONFIG_LDFLAGS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBDIR:INTERNAL=/usr/lib
PKG_FONTCONFIG_LIBRARIES:INTERNAL=fontconfig
PKG_FONTCONFIG_LIBRARY_DIRS:INTERNAL=/usr/lib
PKG_FONTCONFIG_LIBS:INTERNAL=
PKG_FONTCONFIG_LIBS_L:INTERNAL=
PKG_FONTCONFIG_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_MODULE_NAME:INTERNAL=fontconfig
PKG_FONTCONFIG_PREFIX:INTERNAL=/usr
PKG_FONTCONFIG_STATIC_CFLAGS:INTERNAL=-I/usr/include;-I/usr/include/freetype2;-I/usr/include/libpng16;-DWITH_GZFILEOP;-I/usr/include/harfbuzz;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/sysprof-6;-pthread;-DXML_STATIC
PKG_FONTCONFIG_STATIC_CFLAGS_I:INTERNAL=
PKG_FONTCONFIG_STATIC_CFLAGS_OTHER:INTERNAL=-DWITH_GZFILEOP;-pthread;-DXML_STATIC
PKG_FONTCONFIG_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include;/usr/include/freetype2;/usr/include/libpng16;/usr/include/harfbuzz;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/sysprof-6
PKG_FONTCONFIG_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lfontconfig;-pthread;-lm;-lfreetype;-lbz2;-lpng16;-lm;-lm;-lz;-lharfbuzz;-pthread;-lm;-lglib-2.0;-lm;-lsysprof-capture-4;-pthread;-lpcre2-8;-lgraphite2;-lbrotlidec;-lbrotlicommon;-lexpat;-lm
PKG_FONTCONFIG_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-pthread
PKG_FONTCONFIG_STATIC_LIBDIR:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBRARIES:INTERNAL=fontconfig;m;freetype;bz2;png16;m;m;z;harfbuzz;m;glib-2.0;m;sysprof-capture-4;pcre2-8;graphite2;brotlidec;brotlicommon;expat;m
PKG_FONTCONFIG_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
PKG_FONTCONFIG_STATIC_LIBS:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_L:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_OTHER:INTERNAL=
PKG_FONTCONFIG_STATIC_LIBS_PATHS:INTERNAL=
PKG_FONTCONFIG_VERSION:INTERNAL=2.17.1
PKG_FONTCONFIG_fontconfig_INCLUDEDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_LIBDIR:INTERNAL=
PKG_FONTCONFIG_fontconfig_PREFIX:INTERNAL=
PKG_FONTCONFIG_fontconfig_VERSION:INTERNAL=
//ADVANCED property for variable: X11_ICE_INCLUDE_PATH
X11_ICE_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_ICE_LIB
X11_ICE_LIB-ADVANCED:INTERNAL=1
//Have library /usr/lib/libX11.so;/usr/lib/libXext.so
X11_LIB_X11_SOLO:INTERNAL=1
//ADVANCED property for variable: X11_SM_INCLUDE_PATH
X11_SM_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_SM_LIB
X11_SM_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_INCLUDE_PATH
X11_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_LIB
X11_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_INCLUDE_PATH
X11_X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_X11_xcb_LIB
X11_X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_INCLUDE_PATH
X11_XRes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XRes_LIB
X11_XRes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XShm_INCLUDE_PATH
X11_XShm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_XSync_INCLUDE_PATH
X11_XSync_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessrules_INCLUDE_PATH
X11_Xaccessrules_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaccessstr_INCLUDE_PATH
X11_Xaccessstr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_INCLUDE_PATH
X11_Xau_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xau_LIB
X11_Xau_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_INCLUDE_PATH
X11_Xaw_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xaw_LIB
X11_Xaw_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_INCLUDE_PATH
X11_Xcomposite_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcomposite_LIB
X11_Xcomposite_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_INCLUDE_PATH
X11_Xcursor_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xcursor_LIB
X11_Xcursor_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_INCLUDE_PATH
X11_Xdamage_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdamage_LIB
X11_Xdamage_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdbe_INCLUDE_PATH
X11_Xdbe_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_INCLUDE_PATH
X11_Xdmcp_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xdmcp_LIB
X11_Xdmcp_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_INCLUDE_PATH
X11_Xext_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xext_LIB
X11_Xext_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_INCLUDE_PATH
X11_Xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xfixes_LIB
X11_Xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_INCLUDE_PATH
X11_Xft_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xft_LIB
X11_Xft_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_INCLUDE_PATH
X11_Xi_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xi_LIB
X11_Xi_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_INCLUDE_PATH
X11_Xinerama_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xinerama_LIB
X11_Xinerama_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkb_INCLUDE_PATH
X11_Xkb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xkblib_INCLUDE_PATH
X11_Xkblib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xlib_INCLUDE_PATH
X11_Xlib_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_INCLUDE_PATH
X11_Xmu_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xmu_LIB
X11_Xmu_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_INCLUDE_PATH
X11_Xpm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpm_LIB
X11_Xpm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpresent_INCLUDE_PATH
X11_Xpresent_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xpresent_LIB
X11_Xpresent_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_INCLUDE_PATH
X11_Xrandr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrandr_LIB
X11_Xrandr_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_INCLUDE_PATH
X11_Xrender_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xrender_LIB
X11_Xrender_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xshape_INCLUDE_PATH
X11_Xshape_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_INCLUDE_PATH
X11_Xss_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xss_LIB
X11_Xss_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_INCLUDE_PATH
X11_Xt_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xt_LIB
X11_Xt_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_INCLUDE_PATH
X11_Xtst_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xtst_LIB
X11_Xtst_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xutil_INCLUDE_PATH
X11_Xutil_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_INCLUDE_PATH
X11_Xv_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xv_LIB
X11_Xv_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_INCLUDE_PATH
X11_Xxf86misc_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86misc_LIB
X11_Xxf86misc_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_INCLUDE_PATH
X11_Xxf86vm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_Xxf86vm_LIB
X11_Xxf86vm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_dpms_INCLUDE_PATH
X11_dpms_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_INCLUDE_PATH
X11_xcb_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_LIB
X11_xcb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_composite_INCLUDE_PATH
X11_xcb_composite_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_composite_LIB
X11_xcb_composite_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_cursor_INCLUDE_PATH
X11_xcb_cursor_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_cursor_LIB
X11_xcb_cursor_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_damage_INCLUDE_PATH
X11_xcb_damage_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_damage_LIB
X11_xcb_damage_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dpms_INCLUDE_PATH
X11_xcb_dpms_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dpms_LIB
X11_xcb_dpms_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dri2_INCLUDE_PATH
X11_xcb_dri2_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dri2_LIB
X11_xcb_dri2_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dri3_INCLUDE_PATH
X11_xcb_dri3_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_dri3_LIB
X11_xcb_dri3_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_errors_INCLUDE_PATH
X11_xcb_errors_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_errors_LIB
X11_xcb_errors_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_ewmh_INCLUDE_PATH
X11_xcb_ewmh_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_ewmh_LIB
X11_xcb_ewmh_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_glx_INCLUDE_PATH
X11_xcb_glx_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_glx_LIB
X11_xcb_glx_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_INCLUDE_PATH
X11_xcb_icccm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_icccm_LIB
X11_xcb_icccm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_image_INCLUDE_PATH
X11_xcb_image_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_image_LIB
X11_xcb_image_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_keysyms_INCLUDE_PATH
X11_xcb_keysyms_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_keysyms_LIB
X11_xcb_keysyms_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_present_INCLUDE_PATH
X11_xcb_present_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_present_LIB
X11_xcb_present_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_randr_INCLUDE_PATH
X11_xcb_randr_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_randr_LIB
X11_xcb_randr_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_record_INCLUDE_PATH
X11_xcb_record_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_record_LIB
X11_xcb_record_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_render_INCLUDE_PATH
X11_xcb_render_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_render_LIB
X11_xcb_render_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_render_util_INCLUDE_PATH
X11_xcb_render_util_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_render_util_LIB
X11_xcb_render_util_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_res_INCLUDE_PATH
X11_xcb_res_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_res_LIB
X11_xcb_res_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_screensaver_INCLUDE_PATH
X11_xcb_screensaver_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_screensaver_LIB
X11_xcb_screensaver_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_shape_INCLUDE_PATH
X11_xcb_shape_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_shape_LIB
X11_xcb_shape_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_shm_INCLUDE_PATH
X11_xcb_shm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_shm_LIB
X11_xcb_shm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_sync_INCLUDE_PATH
X11_xcb_sync_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_sync_LIB
X11_xcb_sync_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_INCLUDE_PATH
X11_xcb_util_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_util_LIB
X11_xcb_util_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xf86dri_INCLUDE_PATH
X11_xcb_xf86dri_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xf86dri_LIB
X11_xcb_xf86dri_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_INCLUDE_PATH
X11_xcb_xfixes_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xfixes_LIB
X11_xcb_xfixes_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xinerama_INCLUDE_PATH
X11_xcb_xinerama_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xinerama_LIB
X11_xcb_xinerama_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xinput_INCLUDE_PATH
X11_xcb_xinput_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xinput_LIB
X11_xcb_xinput_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xkb_LIB
X11_xcb_xkb_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xrm_INCLUDE_PATH
X11_xcb_xrm_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xrm_LIB
X11_xcb_xrm_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xtest_INCLUDE_PATH
X11_xcb_xtest_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xtest_LIB
X11_xcb_xtest_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xv_INCLUDE_PATH
X11_xcb_xv_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xv_LIB
X11_xcb_xv_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xvmc_INCLUDE_PATH
X11_xcb_xvmc_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xcb_xvmc_LIB
X11_xcb_xvmc_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_INCLUDE_PATH
X11_xkbcommon_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_LIB
X11_xkbcommon_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_INCLUDE_PATH
X11_xkbcommon_X11_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbcommon_X11_LIB
X11_xkbcommon_X11_LIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_INCLUDE_PATH
X11_xkbfile_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: X11_xkbfile_LIB
X11_xkbfile_LIB-ADVANCED:INTERNAL=1
__pkg_config_arguments_GTK3:INTERNAL=REQUIRED;gtk+-3.0
__pkg_config_arguments_PKG_FONTCONFIG:INTERNAL=QUIET;fontconfig
__pkg_config_checked_GTK3:INTERNAL=1
__pkg_config_checked_PKG_FONTCONFIG:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_atk-1.0
pkgcfg_lib_GTK3_atk-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_cairo
pkgcfg_lib_GTK3_cairo-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_cairo-gobject
pkgcfg_lib_GTK3_cairo-gobject-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gdk-3
pkgcfg_lib_GTK3_gdk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gdk_pixbuf-2.0
pkgcfg_lib_GTK3_gdk_pixbuf-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gio-2.0
pkgcfg_lib_GTK3_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_glib-2.0
pkgcfg_lib_GTK3_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gobject-2.0
pkgcfg_lib_GTK3_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gtk-3
pkgcfg_lib_GTK3_gtk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_harfbuzz
pkgcfg_lib_GTK3_harfbuzz-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_pango-1.0
pkgcfg_lib_GTK3_pango-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_pangocairo-1.0
pkgcfg_lib_GTK3_pangocairo-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_z
pkgcfg_lib_GTK3_z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PKG_FONTCONFIG_fontconfig
pkgcfg_lib_PKG_FONTCONFIG_fontconfig-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

