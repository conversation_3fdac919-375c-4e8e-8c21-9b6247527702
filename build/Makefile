# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/cs2-internal

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/cs2-internal/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/cs2-internal/build/CMakeFiles /home/<USER>/Desktop/cs2-internal/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Desktop/cs2-internal/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named cs2-cheat

# Build rule for target.
cs2-cheat: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cs2-cheat
.PHONY : cs2-cheat

# fast build rule for target.
cs2-cheat/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/build
.PHONY : cs2-cheat/fast

external/imgui/backends/imgui_impl_glfw.o: external/imgui/backends/imgui_impl_glfw.cpp.o
.PHONY : external/imgui/backends/imgui_impl_glfw.o

# target to build an object file
external/imgui/backends/imgui_impl_glfw.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_glfw.cpp.o
.PHONY : external/imgui/backends/imgui_impl_glfw.cpp.o

external/imgui/backends/imgui_impl_glfw.i: external/imgui/backends/imgui_impl_glfw.cpp.i
.PHONY : external/imgui/backends/imgui_impl_glfw.i

# target to preprocess a source file
external/imgui/backends/imgui_impl_glfw.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_glfw.cpp.i
.PHONY : external/imgui/backends/imgui_impl_glfw.cpp.i

external/imgui/backends/imgui_impl_glfw.s: external/imgui/backends/imgui_impl_glfw.cpp.s
.PHONY : external/imgui/backends/imgui_impl_glfw.s

# target to generate assembly for a file
external/imgui/backends/imgui_impl_glfw.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_glfw.cpp.s
.PHONY : external/imgui/backends/imgui_impl_glfw.cpp.s

external/imgui/backends/imgui_impl_opengl3.o: external/imgui/backends/imgui_impl_opengl3.cpp.o
.PHONY : external/imgui/backends/imgui_impl_opengl3.o

# target to build an object file
external/imgui/backends/imgui_impl_opengl3.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_opengl3.cpp.o
.PHONY : external/imgui/backends/imgui_impl_opengl3.cpp.o

external/imgui/backends/imgui_impl_opengl3.i: external/imgui/backends/imgui_impl_opengl3.cpp.i
.PHONY : external/imgui/backends/imgui_impl_opengl3.i

# target to preprocess a source file
external/imgui/backends/imgui_impl_opengl3.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_opengl3.cpp.i
.PHONY : external/imgui/backends/imgui_impl_opengl3.cpp.i

external/imgui/backends/imgui_impl_opengl3.s: external/imgui/backends/imgui_impl_opengl3.cpp.s
.PHONY : external/imgui/backends/imgui_impl_opengl3.s

# target to generate assembly for a file
external/imgui/backends/imgui_impl_opengl3.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_opengl3.cpp.s
.PHONY : external/imgui/backends/imgui_impl_opengl3.cpp.s

external/imgui/imgui.o: external/imgui/imgui.cpp.o
.PHONY : external/imgui/imgui.o

# target to build an object file
external/imgui/imgui.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui.cpp.o
.PHONY : external/imgui/imgui.cpp.o

external/imgui/imgui.i: external/imgui/imgui.cpp.i
.PHONY : external/imgui/imgui.i

# target to preprocess a source file
external/imgui/imgui.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui.cpp.i
.PHONY : external/imgui/imgui.cpp.i

external/imgui/imgui.s: external/imgui/imgui.cpp.s
.PHONY : external/imgui/imgui.s

# target to generate assembly for a file
external/imgui/imgui.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui.cpp.s
.PHONY : external/imgui/imgui.cpp.s

external/imgui/imgui_demo.o: external/imgui/imgui_demo.cpp.o
.PHONY : external/imgui/imgui_demo.o

# target to build an object file
external/imgui/imgui_demo.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_demo.cpp.o
.PHONY : external/imgui/imgui_demo.cpp.o

external/imgui/imgui_demo.i: external/imgui/imgui_demo.cpp.i
.PHONY : external/imgui/imgui_demo.i

# target to preprocess a source file
external/imgui/imgui_demo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_demo.cpp.i
.PHONY : external/imgui/imgui_demo.cpp.i

external/imgui/imgui_demo.s: external/imgui/imgui_demo.cpp.s
.PHONY : external/imgui/imgui_demo.s

# target to generate assembly for a file
external/imgui/imgui_demo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_demo.cpp.s
.PHONY : external/imgui/imgui_demo.cpp.s

external/imgui/imgui_draw.o: external/imgui/imgui_draw.cpp.o
.PHONY : external/imgui/imgui_draw.o

# target to build an object file
external/imgui/imgui_draw.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_draw.cpp.o
.PHONY : external/imgui/imgui_draw.cpp.o

external/imgui/imgui_draw.i: external/imgui/imgui_draw.cpp.i
.PHONY : external/imgui/imgui_draw.i

# target to preprocess a source file
external/imgui/imgui_draw.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_draw.cpp.i
.PHONY : external/imgui/imgui_draw.cpp.i

external/imgui/imgui_draw.s: external/imgui/imgui_draw.cpp.s
.PHONY : external/imgui/imgui_draw.s

# target to generate assembly for a file
external/imgui/imgui_draw.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_draw.cpp.s
.PHONY : external/imgui/imgui_draw.cpp.s

external/imgui/imgui_tables.o: external/imgui/imgui_tables.cpp.o
.PHONY : external/imgui/imgui_tables.o

# target to build an object file
external/imgui/imgui_tables.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_tables.cpp.o
.PHONY : external/imgui/imgui_tables.cpp.o

external/imgui/imgui_tables.i: external/imgui/imgui_tables.cpp.i
.PHONY : external/imgui/imgui_tables.i

# target to preprocess a source file
external/imgui/imgui_tables.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_tables.cpp.i
.PHONY : external/imgui/imgui_tables.cpp.i

external/imgui/imgui_tables.s: external/imgui/imgui_tables.cpp.s
.PHONY : external/imgui/imgui_tables.s

# target to generate assembly for a file
external/imgui/imgui_tables.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_tables.cpp.s
.PHONY : external/imgui/imgui_tables.cpp.s

external/imgui/imgui_widgets.o: external/imgui/imgui_widgets.cpp.o
.PHONY : external/imgui/imgui_widgets.o

# target to build an object file
external/imgui/imgui_widgets.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_widgets.cpp.o
.PHONY : external/imgui/imgui_widgets.cpp.o

external/imgui/imgui_widgets.i: external/imgui/imgui_widgets.cpp.i
.PHONY : external/imgui/imgui_widgets.i

# target to preprocess a source file
external/imgui/imgui_widgets.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_widgets.cpp.i
.PHONY : external/imgui/imgui_widgets.cpp.i

external/imgui/imgui_widgets.s: external/imgui/imgui_widgets.cpp.s
.PHONY : external/imgui/imgui_widgets.s

# target to generate assembly for a file
external/imgui/imgui_widgets.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/external/imgui/imgui_widgets.cpp.s
.PHONY : external/imgui/imgui_widgets.cpp.s

src/gui.o: src/gui.cpp.o
.PHONY : src/gui.o

# target to build an object file
src/gui.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/gui.cpp.o
.PHONY : src/gui.cpp.o

src/gui.i: src/gui.cpp.i
.PHONY : src/gui.i

# target to preprocess a source file
src/gui.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/gui.cpp.i
.PHONY : src/gui.cpp.i

src/gui.s: src/gui.cpp.s
.PHONY : src/gui.s

# target to generate assembly for a file
src/gui.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/gui.cpp.s
.PHONY : src/gui.cpp.s

src/hooks.o: src/hooks.cpp.o
.PHONY : src/hooks.o

# target to build an object file
src/hooks.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/hooks.cpp.o
.PHONY : src/hooks.cpp.o

src/hooks.i: src/hooks.cpp.i
.PHONY : src/hooks.i

# target to preprocess a source file
src/hooks.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/hooks.cpp.i
.PHONY : src/hooks.cpp.i

src/hooks.s: src/hooks.cpp.s
.PHONY : src/hooks.s

# target to generate assembly for a file
src/hooks.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/hooks.cpp.s
.PHONY : src/hooks.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cs2-cheat.dir/build.make CMakeFiles/cs2-cheat.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... cs2-cheat"
	@echo "... external/imgui/backends/imgui_impl_glfw.o"
	@echo "... external/imgui/backends/imgui_impl_glfw.i"
	@echo "... external/imgui/backends/imgui_impl_glfw.s"
	@echo "... external/imgui/backends/imgui_impl_opengl3.o"
	@echo "... external/imgui/backends/imgui_impl_opengl3.i"
	@echo "... external/imgui/backends/imgui_impl_opengl3.s"
	@echo "... external/imgui/imgui.o"
	@echo "... external/imgui/imgui.i"
	@echo "... external/imgui/imgui.s"
	@echo "... external/imgui/imgui_demo.o"
	@echo "... external/imgui/imgui_demo.i"
	@echo "... external/imgui/imgui_demo.s"
	@echo "... external/imgui/imgui_draw.o"
	@echo "... external/imgui/imgui_draw.i"
	@echo "... external/imgui/imgui_draw.s"
	@echo "... external/imgui/imgui_tables.o"
	@echo "... external/imgui/imgui_tables.i"
	@echo "... external/imgui/imgui_tables.s"
	@echo "... external/imgui/imgui_widgets.o"
	@echo "... external/imgui/imgui_widgets.i"
	@echo "... external/imgui/imgui_widgets.s"
	@echo "... src/gui.o"
	@echo "... src/gui.i"
	@echo "... src/gui.s"
	@echo "... src/hooks.o"
	@echo "... src/hooks.i"
	@echo "... src/hooks.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

