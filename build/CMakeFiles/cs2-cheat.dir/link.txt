/usr/bin/c++ -fPIC -Wl,--dependency-file=CMakeFiles/cs2-cheat.dir/link.d -shared -Wl,-soname,libcs2-cheat.so -o bin/libcs2-cheat.so "CMakeFiles/cs2-cheat.dir/src/main.cpp.o" "CMakeFiles/cs2-cheat.dir/src/hooks.cpp.o" "CMakeFiles/cs2-cheat.dir/src/gui.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/imgui.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/imgui_demo.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/imgui_draw.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/imgui_tables.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/imgui_widgets.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_opengl3.cpp.o" "CMakeFiles/cs2-cheat.dir/external/imgui/backends/imgui_impl_glfw.cpp.o"  /usr/lib/libOpenGL.so /usr/lib/libGLX.so /usr/lib/libGLU.so /usr/lib/libSM.so /usr/lib/libICE.so /usr/lib/libX11.so /usr/lib/libXext.so -lgtk-3 -lgdk-3 -lz -lpangocairo-1.0 -lcairo-gobject -lgdk_pixbuf-2.0 -latk-1.0 -lpango-1.0 -lcairo -lharfbuzz -lgio-2.0 -lgobject-2.0 -lglib-2.0 -ldl -lpthread
