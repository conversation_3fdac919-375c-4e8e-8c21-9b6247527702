# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/cs2-internal

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/cs2-internal/build

# Include any dependencies generated for this target.
include CMakeFiles/cs2-injector.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/cs2-injector.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/cs2-injector.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/cs2-injector.dir/flags.make

CMakeFiles/cs2-injector.dir/codegen:
.PHONY : CMakeFiles/cs2-injector.dir/codegen

CMakeFiles/cs2-injector.dir/src/injector.cpp.o: CMakeFiles/cs2-injector.dir/flags.make
CMakeFiles/cs2-injector.dir/src/injector.cpp.o: /home/<USER>/Desktop/cs2-internal/src/injector.cpp
CMakeFiles/cs2-injector.dir/src/injector.cpp.o: CMakeFiles/cs2-injector.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/cs2-internal/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/cs2-injector.dir/src/injector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cs2-injector.dir/src/injector.cpp.o -MF CMakeFiles/cs2-injector.dir/src/injector.cpp.o.d -o CMakeFiles/cs2-injector.dir/src/injector.cpp.o -c /home/<USER>/Desktop/cs2-internal/src/injector.cpp

CMakeFiles/cs2-injector.dir/src/injector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/cs2-injector.dir/src/injector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/cs2-internal/src/injector.cpp > CMakeFiles/cs2-injector.dir/src/injector.cpp.i

CMakeFiles/cs2-injector.dir/src/injector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/cs2-injector.dir/src/injector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/cs2-internal/src/injector.cpp -o CMakeFiles/cs2-injector.dir/src/injector.cpp.s

# Object files for target cs2-injector
cs2__injector_OBJECTS = \
"CMakeFiles/cs2-injector.dir/src/injector.cpp.o"

# External object files for target cs2-injector
cs2__injector_EXTERNAL_OBJECTS =

bin/cs2-injector: CMakeFiles/cs2-injector.dir/src/injector.cpp.o
bin/cs2-injector: CMakeFiles/cs2-injector.dir/build.make
bin/cs2-injector: CMakeFiles/cs2-injector.dir/compiler_depend.ts
bin/cs2-injector: CMakeFiles/cs2-injector.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/cs2-internal/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/cs2-injector"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/cs2-injector.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/cs2-injector.dir/build: bin/cs2-injector
.PHONY : CMakeFiles/cs2-injector.dir/build

CMakeFiles/cs2-injector.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/cs2-injector.dir/cmake_clean.cmake
.PHONY : CMakeFiles/cs2-injector.dir/clean

CMakeFiles/cs2-injector.dir/depend:
	cd /home/<USER>/Desktop/cs2-internal/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/cs2-internal /home/<USER>/Desktop/cs2-internal /home/<USER>/Desktop/cs2-internal/build /home/<USER>/Desktop/cs2-internal/build /home/<USER>/Desktop/cs2-internal/build/CMakeFiles/cs2-injector.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/cs2-injector.dir/depend

