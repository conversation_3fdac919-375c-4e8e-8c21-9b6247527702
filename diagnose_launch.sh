#!/bin/bash

# CS2 Launch Diagnostics Script

CHEAT_LIB="$(realpath ./build/bin/libcs2-cheat.so)"
CS2_PATH="/home/<USER>/.steam/steam/steamapps/common/Counter-Strike Global Offensive/game/bin/linuxsteamrt64/cs2"

echo "CS2 Launch Diagnostics"
echo "======================"
echo ""

# Check 1: Library exists and is readable
echo "=== Check 1: Library Status ==="
if [ -f "$CHEAT_LIB" ]; then
    echo "✓ Library exists: $CHEAT_LIB"
    echo "  Size: $(stat -c%s "$CHEAT_LIB") bytes"
    echo "  Permissions: $(stat -c%A "$CHEAT_LIB")"
else
    echo "✗ Library not found: $CHEAT_LIB"
    exit 1
fi

# Check 2: Library dependencies
echo ""
echo "=== Check 2: Library Dependencies ==="
echo "Checking for missing dependencies..."
if ldd "$CHEAT_LIB" | grep -q "not found"; then
    echo "✗ Missing dependencies found:"
    ldd "$CHEAT_LIB" | grep "not found"
    echo ""
    echo "Install missing dependencies and rebuild."
    exit 1
else
    echo "✓ All dependencies satisfied"
fi

# Check 3: CS2 executable
echo ""
echo "=== Check 3: CS2 Executable ==="
if [ -f "$CS2_PATH" ]; then
    echo "✓ CS2 found: $CS2_PATH"
    echo "  Permissions: $(stat -c%A "$CS2_PATH")"
else
    echo "✗ CS2 not found at: $CS2_PATH"
    echo "Searching for CS2..."
    find /home/<USER>"cs2" -type f 2>/dev/null | head -5
fi

# Check 4: Steam runtime compatibility
echo ""
echo "=== Check 4: Steam Runtime Compatibility ==="
echo "Our library architecture: $(file "$CHEAT_LIB" | cut -d: -f2)"
if [ -f "$CS2_PATH" ]; then
    echo "CS2 architecture: $(file "$CS2_PATH" | cut -d: -f2)"
fi

# Check 5: Test simple LD_PRELOAD
echo ""
echo "=== Check 5: LD_PRELOAD Test ==="
echo "Testing LD_PRELOAD with a simple command..."
if LD_PRELOAD="$CHEAT_LIB" /bin/true 2>/dev/null; then
    echo "✓ LD_PRELOAD works with simple commands"
else
    echo "✗ LD_PRELOAD fails even with simple commands"
    echo "Error output:"
    LD_PRELOAD="$CHEAT_LIB" /bin/true
fi

# Check 6: Steam process check
echo ""
echo "=== Check 6: Steam Status ==="
if pgrep -x steam >/dev/null; then
    echo "✓ Steam is running"
    echo "Steam processes:"
    pgrep -l steam | head -3
else
    echo "✗ Steam is not running"
    echo "Start Steam first, then try launching CS2"
fi

# Check 7: Create a minimal test
echo ""
echo "=== Check 7: Minimal Library Test ==="
echo "Creating minimal test library..."

cat > /tmp/test_minimal.c << 'EOF'
#include <stdio.h>

__attribute__((constructor))
void test_load() {
    printf("TEST: Minimal library loaded successfully!\n");
    fflush(stdout);
}
EOF

gcc -shared -fPIC -o /tmp/libtest_minimal.so /tmp/test_minimal.c

echo "Testing minimal library with LD_PRELOAD..."
if LD_PRELOAD="/tmp/libtest_minimal.so" echo "test" 2>/dev/null | grep -q "TEST:"; then
    echo "✓ Minimal LD_PRELOAD test successful"
else
    echo "✗ Even minimal LD_PRELOAD fails"
fi

# Cleanup
rm -f /tmp/test_minimal.c /tmp/libtest_minimal.so

# Check 8: Suggest solutions
echo ""
echo "=== Suggested Solutions ==="
echo ""
echo "1. **Try launching CS2 from terminal:**"
echo "   cd ~/.steam/steam/steamapps/common/Counter-Strike\\ Global\\ Offensive/"
echo "   LD_PRELOAD=\"$CHEAT_LIB\" ./game/bin/linuxsteamrt64/cs2 -insecure -allow_third_party_software"
echo ""
echo "2. **Try with Steam launch options (exact format):**"
echo "   LD_PRELOAD=\"$CHEAT_LIB\" %command% -insecure -allow_third_party_software"
echo ""
echo "3. **Try without Steam runtime:**"
echo "   STEAM_COMPAT_CLIENT_INSTALL_PATH=\"\" LD_PRELOAD=\"$CHEAT_LIB\" %command% -insecure -allow_third_party_software"
echo ""
echo "4. **Check Steam logs:**"
echo "   ~/.steam/steam/logs/console_log.txt"
echo ""
echo "5. **Alternative: Create wrapper script:**"
echo "   Create a wrapper script that Steam can execute"

# Check 9: Create wrapper script
echo ""
echo "=== Creating Wrapper Script ==="
cat > cs2_with_cheat.sh << EOF
#!/bin/bash
export LD_PRELOAD="$CHEAT_LIB"
exec "$CS2_PATH" "\$@"
EOF

chmod +x cs2_with_cheat.sh
echo "✓ Created wrapper script: $(pwd)/cs2_with_cheat.sh"
echo ""
echo "To use wrapper script in Steam launch options:"
echo "$(pwd)/cs2_with_cheat.sh -insecure -allow_third_party_software"
